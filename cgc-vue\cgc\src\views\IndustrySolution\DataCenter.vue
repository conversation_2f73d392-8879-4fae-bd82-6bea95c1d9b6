<template>
  <div class="power-monitoring-page">
    <!-- Header -->

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">轻量化边缘智能方案, 融合三维可视化与AI节能策略, 实现机房能效优化、安全零死角、碳流精准追踪。</h1>
        <p class="hero-subtitle">A full process solution covering energy management, power monitoring, and asset management</p>
      </div>
      <img src="/public/images/台式机@1x.png" alt="">
    </section>

    <!-- 3D Platform Section - Redesigned -->
    <section class="platform-section-redesigned">
      <div class="power-monitoring-container">
        <!-- Background Image -->
        <div class="background-image">
          <img src="/public/images/<EMAIL>" alt="Power Monitoring System" />
        </div>

        <!-- Coded Elements Overlay -->
        <div class="overlay-elements">
          <!-- Top Section - Main Title and Buttons -->
          <div class="top-section">
            <div class="solution-badge">数据中心解决方案</div>
            <h1 class="main-title">智控配电   孪生增效</h1>
            <p class="subtitle">
              通过三维可视化、全面监控、多维度能耗分析、自动化节能控制及异常预警，助力决策、降本增效，全方位保障配电安全与高效运行。
            </p>
            <div class="action-buttons">
              <button class="btn-smart">
                <span class="btn-icon">●</span>
                边缘自优化
              </button>
              <button class="btn-smart">
                <span class="btn-icon">●</span>
                碳流可追溯
              </button>
            </div>
          </div>

          <!-- Top Left Card - 连接收集 -->
          <div class="card top-left-card">
            <div class="card-header">
              <div class="card-icon red-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <rect x="2" y="3" width="12" height="8" rx="1" fill="currentColor"/>
                  <rect x="4" y="5" width="8" height="1" fill="white"/>
                  <rect x="4" y="7" width="6" height="1" fill="white"/>
                  <rect x="4" y="9" width="4" height="1" fill="white"/>
                </svg>
              </div>
              <h3>动态PUE优化</h3>
            </div>
            <p class="card-subtitle">传感节点实时采集耗能设备电</p>
            <p class="card-subtitle">流/功率数据，定位高耗能设</p>
            <p class="card-subtitle">备</p>
          </div>

          <!-- Bottom Left Card - 监测分析 -->
          <div class="card bottom-left-card">
            <div class="card-header">
              <div class="card-icon red-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M2 12L6 8L9 11L14 4" stroke="currentColor" stroke-width="2" fill="none"/>
                  <circle cx="6" cy="8" r="1.5" fill="currentColor"/>
                  <circle cx="9" cy="11" r="1.5" fill="currentColor"/>
                  <circle cx="14" cy="4" r="1.5" fill="currentColor"/>
                </svg>
              </div>
              <h3>安防监控</h3>
            </div>
            <div class="card-items">
              <div class="card-item">手动精细化控制</div>
              <div class="card-item">自动执行节能策略</div>

            </div>
          </div>

          <!-- Top Right Card - 治理优化 -->
          <div class="card top-right-card">
            <div class="card-header">
              <div class="card-icon green-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M13 4L6 11L3 8" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <h3>动环监测</h3>
            </div>
            <div class="card-items">
              <div class="card-item">AI优化分析</div>
              <div class="card-item">高碳排设备排行</div>
              <div class="card-item">设备能效数据</div>
            </div>
          </div>
          <img src="/public/images/lefttop.png" alt="" class="left-top-image" />
          <img src="/public/images/leftbutton.png" alt="" class="left-button-image">
          <img src="/public/images/leftxian.png" alt="" class="left-xian-image">
        </div>
      </div>
    </section>

    <!-- Text and Features Container -->
    <section class="text-features-container">
      <!-- Text Section -->
      <div class="text-section">
        <div class="text-content">
          <div class="text-badge"><span class="text-badge-content">数据中心面临的挑战</span></div>
          <p class="main-text">通过智能监控与精准调控，打造安全无忧、高效节能、管理精细的智慧机房</p>
        </div>
      </div>

      <!-- Features Section -->
      <div class="features-section">
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon01.svg" alt=""></div>
            <h3>传统监控难以直观呈现配电全貌，数据展示零散</h3>
          </div>
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon02.svg" alt=""></div>
            <h3>能源流向不明，缺乏精准分析与节能策略</h3>
          </div>
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon03.svg" alt=""></div>
            <h3>故障定位慢，人工处置不及时，风险高</h3>
          </div>
        </div>
      </div>
    </section>

    <!-- Monitoring Section - Redesigned -->
    <section class="monitoring-section-redesigned">
      <div class="curved-container">
        <!-- Background curves -->
     
        
        <div class="monitoring-content-new">
          <div class="section-header">
            <h2 class="monitoring-title-new">
              轻量化部署与技术融合，能效、安全及<br />
              碳管理的全面提升
            </h2>
            <p class="monitoring-subtitle-new">轻边缘智联孪生 AI，能效安全双优，碳流追踪无遗</p>
          </div>

          <div class="monitoring-tabs-new">
            <button 
              v-for="(tab, index) in newTabs" 
              :key="index"
              :class="['tab-btn-new', { active: activeNewTab === index }]"
              @click="activeNewTab = index"
            >
              {{ tab }}
            </button>
          </div>

          <div class="monitoring-display-new">
            <div class="monitoring-info-new">
              <h3>三维可视化</h3>
              <p>
                机房物理实体(建筑、环境、设备等)虚拟三维数字模型和虚拟数字场景的综合展示，提供更直观的管理视角。
              </p>
            </div>

            <div class="monitoring-stats-new">
              <div class="stat-item-new">
                <span class="stat-label-new">三维可视化关键数据展示

</span>
              </div>
              <div class="stat-item-new">
                <span class="stat-label-new">多视图可视化</span>
              </div>
              <div class="stat-item-new">
                <span class="stat-label-new">检修策略自动生成</span>
              </div>
            </div>
          </div>

          <!-- 电脑图片展示区域 -->
          <div class="computer-display-container">
            <div class="computer-image-wrapper">
              <!-- 电脑框架 -->
              <img src="/public/images/电脑框.png" alt="电脑框架" class="computer-frame" />
              <!-- 内嵌内容 -->
              <div class="computer-screen">
                <img src="/public/images/三维巡检——配电房@1x (2)@1x (1).png" alt="电力监控系统界面" class="computer-content" />
              </div>
            </div>
            <!-- 指示点 -->
            <div class="computer-indicators">
              <span class="indicator-oval"></span>
              <span class="indicator-dot"></span>
              <span class="indicator-dot"></span>
              <span class="indicator-dot"></span>
              <span class="indicator-dot"></span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Application Scenarios -->
    <section class="scenarios-section">
      <h2 class="scenarios-title">数据中心应用场景</h2>
      <p class="scenarios-subtitle">打造安全、节能、管理精细化的机房</p>

      <div class="scenarios-grid">
        <div class="scenario-card">
          <div class="scenario-content">
            <h3>云计算集群</h3>
            <p>空调AI调优，PUE压降至1.25</p>
            <div class="scenario-tags">
              <span class="tag">智能控温</span>
              <span class="tag">节能降耗</span>
            </div>
          </div>
          <div class="hover-content" style="background-image: url('/images/Image-2.png')">
            <div class="hover-overlay">
              <span class="hover-tag">云计算集群</span>
              <h4>智能算法动态调控，实现PUE极致优化与绿色节能</h4>
            </div>
          </div>
        </div>

        <div class="scenario-card">
          <div class="scenario-content">
            <h3>金融数据中心</h3>
            <p>三维安防联动，非法入侵秒级阻断</p>
            <div class="scenario-tags">
              <span class="tag">安全至上</span>
              <span class="tag">秒级响应</span>
            </div>
          </div>
          <div class="hover-content" style="background-image: url('/images/Image-3.png')">
            <div class="hover-overlay">
              <span class="hover-tag">金融数据中心</span>
              <h4>毫秒级安防响应，构筑物理与数据资产的坚固防线</h4>
            </div>
          </div>
        </div>

        <div class="scenario-card">
          <div class="scenario-content">
            <h3>超算中心</h3>
            <p>三维热力图预警，故障率归零</p>
            <div class="scenario-tags">
              <span class="tag">稳压护航</span>
              <span class="tag">严控波动</span>
            </div>
          </div>
          <div class="hover-content" style="background-image: url('/images/Image-4.png')">
            <div class="hover-overlay">
              <span class="hover-tag">超算中心</span>
              <h4>重视电源稳定性，保障精密设备免受配电波动影响</h4>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    
  </div>
</template>

<script setup>
import { ref } from 'vue'

const activeTab = ref(0)
const activeDot = ref(0)
const activeNewTab = ref(0)
const activeNewDot = ref(0)

const tabs = [
  '实时监控',
  '三维建模管理', 
  '数字孪生建设',
  '电能质量分析',
  '能效效率分析'
]

const newTabs = [
  '三维可视化',
  '实时监控',
  '能源管理',
  '场景联动',
  '实时预警'
]
</script>

<style scoped>
.power-monitoring-page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
}

/* Header */
.header {
  background: #2c3e50;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  height: 60px;
}

.logo img {
  height: 30px;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #ecf0f1;
  text-decoration: none;
  font-size: 14px;
  padding: 0.5rem 0;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.nav-link:hover,
.nav-link.active {
  color: #e74c3c;
  border-bottom-color: #e74c3c;
}

.header-right {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.header-right a {
  color: #ecf0f1;
  text-decoration: none;
  font-size: 12px;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.header-right a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Hero Section */
.hero-section {
  height: 100vh;
  background-color:#1e1f25 ;
  background-image: url("/public/images/组 <EMAIL>"),url("/public/images/<EMAIL>"),url("/public/images/Grid <EMAIL>");
  background-size: 42% auto, 80% auto, cover; /* 1st main, 2nd decorative, 3rd grid */
  background-position: 75% center, 20% center, center; /* move 2nd left */
  background-repeat: no-repeat, no-repeat, no-repeat;
  display: flex;
  align-items: center;
  padding-left: 8%;
  position: relative;
  margin-top: 60px;
}
.hero-section img{
  width: 60%;
  height: 60%;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: rgba(0, 0, 0, 0.4); */
}

.hero-content {
  flex: 1;
  max-width: 45%;
  color: white;
}

.hero-title {
  font-size: 2.2rem;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: 1.1rem;
  color: #a0a0a0;
  line-height: 1.6;
  max-width: 80%;
}

.hero-section > img {
  flex: 1;
  max-width:40%;
  height: auto;
  object-fit: contain;
}

/* Platform Section Redesigned */
.platform-section-redesigned {
  padding: 0;
  margin: 0;
}

.power-monitoring-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #2a2a2a;
}

.background-image {
  background-color: #1e1f25;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-image img {
  width: 70%;
  height: 70%;
  object-fit: contain;
  position: absolute;
  left: 55%;
  top: 70%;
  transform: translate(-50%, -50%);
}

.overlay-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

/* Top Section */
.top-section {
  position: absolute;
  top: 8%;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  max-width: 800px;
  width: 90%;
}

.solution-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

.main-title {
  font-size: 2.2rem;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 16px;
  color: white;
}

.subtitle {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 30px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.btn-smart,
.btn-precise {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 25px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-smart {
  background: white;
  color: #333;
}

.btn-precise {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* Cards */
.card {
  position: absolute;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.card-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.red-icon {
  background: #e74c3c;
}

.green-icon {
  background: #27ae60;
}

.card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.card-subtitle {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.card-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-item {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}



/* Card Positions */
.top-left-card {
  top: 32%;
  left: 12%;
}

.bottom-left-card {
  bottom: 5%;
  left: 8%;
}

.top-right-card {
  top: 28%;
  right: 8%;
}

/* Text and Features Container */
.text-features-container {
  background-image: url("/public/images/有一家设计素材铺淘宝店 <EMAIL>");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 6rem 0;
}

/* Text Section */
.text-section {
  padding-top: 2rem;
  text-align: center;
}

.text-badge {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.text-badge-content {
  background: rgba(255, 255, 255, 0.9);
  color: #666666;
  font-size: 14px;
  padding: 12px 24px;
  border-radius: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.main-text {
  font-size: 1.8rem;
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 400;
  max-width: 800px;
  margin: 0 auto;
}

/* Features Section */
.features-section {
  padding: 4rem 8% 2rem 8%;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: #ffffff;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #ffffff;
  margin: 0 auto 1.5rem;
}



.feature-card h3 {
  font-size: 14px;
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 400;
}

/* Monitoring Section Redesigned */
.monitoring-section-redesigned {
  position: relative;
  background-image: url("/public/images/<EMAIL>");
  background-size: 100% 100%;
  background-position: center center;
  background-repeat: no-repeat;
  min-height: 170vh;
  height: auto;
  padding: 80px 0 0 0;
  margin-bottom: -150px;
  z-index: 2;
  overflow: visible;
}

.curved-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: inherit;
}

.curve-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: #ecf0f1;
  clip-path: ellipse(100% 100% at 50% 0%);
  z-index: 1;
}

.curve-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: #ecf0f1;
  clip-path: ellipse(100% 100% at 50% 100%);
  z-index: 1;
}

.monitoring-content-new {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  min-height: 140vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-bottom: 4rem;
  color: #ffffff;
}

.section-header {
  text-align: center;
  margin-bottom: 1.5rem;
  padding-top: 1rem;
}

.monitoring-title-new {
  font-size: 3rem;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 0.8rem;
  color: white;
  letter-spacing: 1px;
}

.monitoring-subtitle-new {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0;
  font-weight: 300;
}

.monitoring-tabs-new {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0 2.5rem 0;
  flex-wrap: wrap;
  position: relative;
}

.monitoring-tabs-new::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.tab-btn-new {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 12px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  border-bottom: 2px solid transparent;
}

.tab-btn-new.active {
  color: #e74c3c;
  border-bottom-color: #e74c3c;
}

.tab-btn-new:hover {
  color: #e74c3c;
}

.monitoring-display-new {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: flex-start;
  max-width: 1200px;
  margin: 1.5rem auto 0;
  padding: 0 2rem;
}

.monitoring-info-new h3 {
  font-size: 1.6rem;
  margin-bottom: 1.5rem;
  color: white;
  font-weight: 600;
}

.monitoring-info-new p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.8;
  margin-bottom: 0;
  font-size: 14px;
}

/* Features Section (cards with icon and title) */
.features-section {
  padding: 4rem 8% 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.06);
  padding: 1.25rem 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.feature-icon.red {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-icon.red img {
  width: 28px;
  height: 28px;
  transition: filter 0.3s ease;
}

.feature-card h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.5;
}

.feature-card:hover {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  transform: translateY(-4px);
  box-shadow: 0 14px 36px rgba(0,0,0,0.12);
}

.feature-card:hover h3 {
  color: #ffffff;
}

.feature-card:hover .feature-icon.red {
  background: rgba(255, 255, 255, 0.15);
}

.feature-card:hover .feature-icon.red img {
  filter: brightness(0) invert(1);
}

.monitoring-stats-new {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  margin-top: 0;
  text-align: right;
  align-items: flex-end;
}

.stat-item-new {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.1rem 0;
  justify-content: flex-end;
}

.stat-number-new {
  font-size: 2.2rem;
  font-weight: bold;
  color: #e74c3c;
  min-width: 60px;
}

.stat-label-new {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.2;
}

/* 电脑图片展示区域样式 */
.computer-display-container {
  position: relative;
  margin-top: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
}

.computer-image-wrapper {
  position: relative;
  width: 100%;
  max-width: 800px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.computer-frame {
  width: 100%;
  height: auto;
  max-width: 800px;
  object-fit: contain;
  position: relative;
  z-index: 2;
}

.computer-screen {
  position: absolute;
  top: 10%;
  left: 12%;
  width: 76%;
  height: 66%;
  overflow: hidden;
  border-radius: 6px;
  z-index: 1;
}

.computer-content {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* 指示点样式 */
.computer-indicators {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}


.indicator-oval {
  width: 20px;
  height: 8px;
  border-radius: 4px;
  background-color: #ff4444;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ff4444;
  cursor: pointer;
  transition: all 0.3s ease;
}

.laptop-mockup-new {
  position: relative;
  perspective: 1000px;
}

.laptop-screen-new {
  width: 600px;
  height: 380px;
  background: #1a1a1a;
  border-radius: 12px 12px 0 0;
  border: 8px solid #2c3e50;
  border-bottom: none;
  transform: rotateX(8deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  overflow: hidden;
}

.screen-image-new {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.laptop-base-new {
  width: 620px;
  height: 20px;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  border-radius: 0 0 20px 20px;
  position: absolute;
  top: 380px;
  left: -10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.laptop-base-new::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 4px;
  background: #7f8c8d;
  border-radius: 2px;
}

.laptop-dots-new {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 2rem;
}

.dot-new {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: background-color 0.3s;
}

.dot-new.active {
  background: #e74c3c;
}

/* Scenarios Section */
.scenarios-section {
  position: relative;
  background-image: url("/public/images/有一家设计素材铺淘宝店 <EMAIL>");
  /* 让背景图片居中显示 */
  background-position: center;
  padding: 200px 8% 8rem 8%;
  z-index: 1;
}

.scenarios-title {
  font-size: 2.5rem;
  font-weight: 500;
  text-align: center;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.scenarios-subtitle {
  font-size: 1rem;
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 4rem;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.scenario-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 240px;
}

.scenario-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.scenario-content {
  padding: 2rem;
  text-align: left;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: opacity 0.4s ease;
  box-sizing: border-box;
}

.scenario-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: #333333;
  line-height: 1.4;
}

.scenario-content p {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 1.2rem;
  flex-grow: 1;
}

.scenario-tags {
  display: flex;
  gap: 0.6rem;
}

.tag {
  background: #f5f5f5;
  color: #666666;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 0.75rem;
}

.hover-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.scenario-card:hover .scenario-content {
  opacity: 0;
}

.scenario-card:hover .hover-content {
  opacity: 1;
  pointer-events: auto;
}

.hover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  box-sizing: border-box;
}

.hover-tag {
  display: inline-block;
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  padding: 5px 15px;
  font-size: 0.8rem;
  margin-bottom: 0.8rem;
}

.hover-overlay h4 {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
  line-height: 1.4;

  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

/* Footer */
.footer {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 4rem 8% 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 4rem;
}

.footer-logo img {
  height: 30px;
  margin-bottom: 1rem;
}

.footer-desc {
  color: #bdc3c7;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: background-color 0.3s;
}

.social-link:hover {
  background: #e74c3c;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.link-group h4 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 16px;
}

.link-group a {
  display: block;
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  margin-bottom: 0.5rem;
  transition: color 0.3s;
}

.link-group a:hover {
  color: #e74c3c;
}

.footer-contact h4 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 14px;
  color: #bdc3c7;
}

.contact-icon {
  font-size: 16px;
}

.footer-bottom {
  max-width: 1200px;
  margin: 2rem auto 0;
  padding-top: 2rem;
  border-top: 1px solid #34495e;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  color: #bdc3c7;
  font-size: 14px;
}

.footer-bottom-links {
  display: flex;
  gap: 2rem;
}

.footer-bottom-links a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.footer-bottom-links a:hover {
  color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-title {
    font-size: 1.8rem;
  }
  
  .top-left-card {
    left: 8%;
  }
  
  .bottom-left-card {
    left: 5%;
  }
  
  .top-right-card {
    right: 5%;
  }

  .monitoring-display-new {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .laptop-screen-new {
    width: 500px;
    height: 320px;
  }
}

@media (max-width: 1024px) {
  .header-content {
    padding: 0 1rem;
  }

  .nav {
    gap: 1rem;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .scenario-card {
    height: 220px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 1rem;
  }

  .nav {
    margin: 1rem 0;
  }

  .header-right {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .hero-section {
    padding: 0 5%;
    margin-top: 120px;
  }

  .hero-title {
    font-size: 1.2rem;
  }

  .text-features-container {
    padding: 4rem 0;
  }

  .text-section,
  .features-section,
  .scenarios-section {
    padding: 2rem 5%;
  }

  .main-text {
    font-size: 1.3rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .monitoring-title-new {
    font-size: 2.2rem;
  }

  .monitoring-tabs-new {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .laptop-screen-new {
    width: 350px;
    height: 220px;
  }

  .monitoring-stats-new {
    gap: 0.5rem;
  }

  .computer-display-container {
    margin: 2rem auto 0;
    padding: 0 1rem;
    min-height: 300px;
  }

  .computer-image-wrapper {
    max-width: 100%;
  }

  .computer-image {
    max-width: 100%;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .scenario-card {
    height: 200px;
  }

  .scenario-content {
    padding: 1rem;
  }

  .scenario-content h3 {
    font-size: 1.1rem;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  /* Redesigned section mobile */
  .top-section {
    top: 5%;
    width: 95%;
  }
  
  .main-title {
    font-size: 1.4rem;
  }
  
  .subtitle {
    font-size: 0.9rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  
  .card {
    min-width: 160px;
    padding: 16px;
  }
  
  .top-left-card {
    top: 45%;
    left: 5%;
  }
  
  .bottom-left-card {
    bottom: 15%;
    left: 5%;
  }
  
  .top-right-card {
    top: 45%;
    right: 5%;
  }

}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1rem;
  }

  .main-text {
    font-size: 1.1rem;
  }

  .monitoring-title-new {
    font-size: 1.8rem;
  }

  .scenarios-title {
    font-size: 1.5rem;
  }

  .laptop-screen-new {
    width: 280px;
    height: 180px;
  }

  .monitoring-tabs-new {
    flex-direction: column;
    align-items: center;
  }

  /* Redesigned section mobile */
  .monitoring-section-redesigned {
    min-height: 120vh;
    padding: 60px 0 100px 0;
    background-size: 100% 100%;
  }

  .monitoring-content-new {
    min-height: 120vh;
  }

  .main-title {
    font-size: 1.2rem;
  }
  
  .card {
    min-width: 140px;
    padding: 12px;
  }
  
  .card h3 {
    font-size: 14px;
  }
  
  .card-subtitle,
  .card-item {
    font-size: 12px;
  }
}
.left-top-image{
  position: absolute;
  top:41%;
  left:22%;
}
.left-button-image{
  position: absolute;
  top:60%;
  left:15%;
}
.left-xian-image{
  position: absolute;
  top:54%;
  right:17%;
}
</style>
<template>
  <div class="power-monitoring-page">
    <!-- Header -->

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="hero-badge">三维智能联动平台实现全球精准决策与毫秒级响应。</div>
        <h1 class="hero-title">
          构建变电站级三维孪生体，融合SCADA实时数据与设备历史档案，实现从主变压器到绝缘子的全生命周期健康管理，支持设备拆解透视与故障回溯
        </h1>
        <button class="hero-button">See more</button>
      </div>
    </section>

    <!-- 3D Platform Section - Redesigned -->
    <section class="platform-section-redesigned">
      <div class="power-monitoring-container">
        <!-- Background Image -->
        <div class="background-image">
          <img src="/public/images/<EMAIL>" alt="Power Monitoring System" />
        </div>

        <!-- Coded Elements Overlay -->
        <div class="overlay-elements">
          <!-- Top Section - Main Title and Buttons -->
          <div class="top-section">
            <div class="solution-badge">一体化解决方案</div>
            <h1 class="main-title">覆盖配电的设计、建造、运营、维护的全场景应用</h1>
            <p class="subtitle">
              满足配电监控需求，可扩展强电一体化监控，作为电力专业子系统为
              客户的定制管理系统提供实时电力数据。
            </p>
            <div class="action-buttons">
              <button class="btn-smart">
                <span class="btn-icon">●</span>
                智能分析
              </button>
              <button class="btn-smart">
                <span class="btn-icon">●</span>
                精准决策
              </button>
            </div>
          </div>

          <!-- Top Left Card - 连接收集 -->
          <div class="card top-left-card">
            <div class="card-header">
              <div class="card-icon red-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <rect x="2" y="3" width="12" height="8" rx="1" fill="currentColor"/>
                  <rect x="4" y="5" width="8" height="1" fill="white"/>
                  <rect x="4" y="7" width="6" height="1" fill="white"/>
                  <rect x="4" y="9" width="4" height="1" fill="white"/>
                </svg>
              </div>
              <h3>连接收集</h3>
            </div>
            <p class="card-subtitle">预置数字化元件，智能化终端</p>
          </div>

          <!-- Bottom Left Card - 监测分析 -->
          <div class="card bottom-left-card">
            <div class="card-header">
              <div class="card-icon red-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M2 12L6 8L9 11L14 4" stroke="currentColor" stroke-width="2" fill="none"/>
                  <circle cx="6" cy="8" r="1.5" fill="currentColor"/>
                  <circle cx="9" cy="11" r="1.5" fill="currentColor"/>
                  <circle cx="14" cy="4" r="1.5" fill="currentColor"/>
                </svg>
              </div>
              <h3>监测分析</h3>
            </div>
            <div class="card-items">
              <div class="card-item">电气系统监控</div>
              <div class="card-item">电能质量分析</div>
              <div class="card-item">能效效率分析</div>
            </div>
          </div>

          <!-- Top Right Card - 治理优化 -->
          <div class="card top-right-card">
            <div class="card-header">
              <div class="card-icon green-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M13 4L6 11L3 8" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <h3>治理优化</h3>
            </div>
            <div class="card-items">
              <div class="card-item">资产健康管理</div>
              <div class="card-item">三维巡检业务</div>
              <div class="card-item">AI优化分析</div>
            </div>
          </div>
          <img src="/public/images/lefttop.png" alt="" class="left-top-image" />
          <img src="/public/images/leftbutton.png" alt="" class="left-button-image">
          <img src="/public/images/leftxian.png" alt="" class="left-xian-image">
        </div>
      </div>
    </section>

    <!-- Text and Features Container -->
    <section class="text-features-container">
      <!-- Text Section -->
      <div class="text-section">
        <div class="text-content">
          <div class="text-badge"><span class="text-badge-content">服务遍及力量强劲的国际市场和国内市场的所有领域</span></div>
          <p class="main-text">在国际局势复杂多变与低碳发展双重驱动下，依托自主可控技术保障电力系统稳定高效运行</p>
        </div>
      </div>

      <!-- Features Section -->
      <div class="features-section">
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon01.svg" alt=""></div>
            <h3>发电侧智能化，智能化管理运维工程技术服务体系建设，实现智能化</h3>
          </div>
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon02.svg" alt=""></div>
            <h3>在智能电网建设，运行200MW工程技术服务体系建设，实现智能化管理</h3>
          </div>
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon03.svg" alt=""></div>
            <h3>广泛应用于工，中国企业数据库管理一体化服务体系，实现智能化</h3>
          </div>
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon04.svg" alt=""></div>
            <h3>智力发展战略化，智能化管理运维工程技术服务体系建设，实现智能化管理</h3>
          </div>
        </div>
      </div>
    </section>

    <!-- Monitoring Section - Redesigned -->
    <section class="monitoring-section-redesigned">
      <div class="curved-container">
        <!-- Background curves -->
     
        
        <div class="monitoring-content-new">
          <div class="section-header">
            <h2 class="monitoring-title-new">
              全面打通连接、监测、治理的全数字化<br />
              流程，形成智能配电闭环价值落地
            </h2>
            <p class="monitoring-subtitle-new">实时监控一体化运行方案，打造智能一体化全生命周期管理</p>
          </div>

          <div class="monitoring-tabs-new">
            <button 
              v-for="(tab, index) in newTabs" 
              :key="index"
              :class="['tab-btn-new', { active: activeNewTab === index }]"
              @click="activeNewTab = index"
            >
              {{ tab }}
            </button>
          </div>

          <div class="monitoring-display-new">
            <div class="monitoring-info-new">
              <h3>实时监控</h3>
              <p>
                通过实时资产监控，准确的故障识别和用户驱动控制，提高性能
                并降低运营成本。保持恒定的控制与数据流技术应用到一秒的间
                隔，确保你总是有你的手指在脉搏上。
              </p>
            </div>

            <div class="monitoring-stats-new">
              <div class="stat-item-new">
               
                <span class="stat-label-new">12维健康模型动态评分</span>
              </div>
              <div class="stat-item-new">
                <span class="stat-label-new">告警穿透定位</span>
              </div>
              <div class="stat-item-new">
                <span class="stat-label-new">检修策略自动生成</span>
              </div>
            </div>
          </div>

          <!-- 电脑图片展示区域 -->
          <div class="computer-display-container">
            <div class="computer-image-wrapper">
              <!-- 电脑框架 -->
              <img src="/public/images/电脑框.png" alt="电脑框架" class="computer-frame" />
              <!-- 内嵌内容 -->
              <div class="computer-screen">
                <img src="/public/images/三维巡检——配电房@1x (2)@1x.png" alt="电力监控系统界面" class="computer-content" />
              </div>
            </div>
            <!-- 指示点 -->
            <div class="computer-indicators">
              <span class="indicator-oval"></span>
              <span class="indicator-dot"></span>
              <span class="indicator-dot"></span>
              <span class="indicator-dot"></span>
              <span class="indicator-dot"></span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Application Scenarios -->
    <section class="scenarios-section">
      <h2 class="scenarios-title">电力监控系统应用场景</h2>
      <p class="scenarios-subtitle">三大类电力用户，N种安定设备，N种解决方案</p>

      <div class="scenarios-grid">
        <div class="scenario-card">
          <div class="scenario-content">
            <h3>老旧电网</h3>
            <p>AI预警绝缘劣化风险，改造效率提升50%</p>
            <div class="scenario-tags">
              <span class="tag">绝缘老化</span>
              <span class="tag">安全预警</span>
            </div>
          </div>
          <div class="hover-content">
            <div class="hover-overlay">
              <h3>老旧电网</h3>
              <p>AI预警绝缘劣化风险，改造效率提升50%</p>
            </div>
          </div>
        </div>

        <div class="scenario-card">
          <div class="scenario-content">
            <h3>城市变电站</h3>
            <p>三维建模设备健康状态，故障定位提速8倍</p>
            <div class="scenario-tags">
              <span class="tag">负荷中心</span>
              <span class="tag">防故障扩散</span>
            </div>
          </div>
          <div class="hover-content">
            <div class="hover-overlay">
              <h3>城市变电站</h3>
              <p>城市负荷中心枢纽站，承担高压转换与区域供电，需密集设备监控防故障扩散</p>
            </div>
          </div>
        </div>

        <div class="scenario-card">
          <div class="scenario-content">
            <h3>新建电厂</h3>
            <p>数字孪生交付资产台账，运维成本直降30%</p>
            <div class="scenario-tags">
              <span class="tag">全数字化交付</span>
              <span class="tag">孪生起点</span>
            </div>
          </div>
          <div class="hover-content">
            <div class="hover-overlay">
              <h3>新建电厂</h3>
              <p>数字孪生交付资产台账，运维成本直降30%</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    
  </div>
</template>

<script setup>
import { ref } from 'vue'

const activeTab = ref(0)
const activeDot = ref(0)
const activeNewTab = ref(0)
const activeNewDot = ref(0)

const tabs = [
  '实时监控',
  '三维建模管理', 
  '数字孪生建设',
  '电能质量分析',
  '能效效率分析'
]

const newTabs = [
  '实时监控',
  '三维建模管理',
  '资产健康管理',
  '电能质量分析',
  '能效效率分析'
]
</script>

<style scoped>
.power-monitoring-page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
}

/* Header */
.header {
  background: #2c3e50;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  height: 60px;
}

.logo img {
  height: 30px;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #ecf0f1;
  text-decoration: none;
  font-size: 14px;
  padding: 0.5rem 0;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.nav-link:hover,
.nav-link.active {
  color: #e74c3c;
  border-bottom-color: #e74c3c;
}

.header-right {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.header-right a {
  color: #ecf0f1;
  text-decoration: none;
  font-size: 12px;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.header-right a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Hero Section */
.hero-section {
  height: 100vh;
  background-image: url('/images/DecorativeLine@1x (1).png'),url('/public/images/<EMAIL>');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  padding-left: 8%;
  position: relative;
  margin-top: 60px;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {

  position: relative;
  z-index: 2;
  max-width: 600px;
}

/* Features Section (cards with icon and title) */
.features-section {
  padding: 4rem 8% 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.06);
  padding: 1.25rem 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.feature-icon.red {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-icon.red img {
  width: 28px;
  height: 28px;
  transition: filter 0.3s ease;
}

.feature-card h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.5;
}

.feature-card:hover {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  transform: translateY(-4px);
  box-shadow: 0 14px 36px rgba(0,0,0,0.12);
}

.feature-card:hover h3 {
  color: #ffffff;
}

.feature-card:hover .feature-icon.red {
  background: rgba(255, 255, 255, 0.15);
}

.feature-card:hover .feature-icon.red img {
  filter: brightness(0) invert(1);
}

.hero-badge {
  background: rgba(255, 255, 255, 0.9);
  color: #2c3e50;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  margin-bottom: 2rem;
  display: inline-block;
}

.hero-title {
  font-size: 1.8rem;
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 3rem;
  line-height: 1.6;
}

.hero-button {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.hero-button:hover {
  background-color: #c0392b;
}

/* Platform Section Redesigned */
.platform-section-redesigned {
  padding: 0;
  margin: 0;
}

.power-monitoring-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #2a2a2a;
}

.background-image {
  background-color: #000100;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-image img {
  width: 50%;
  height: 60%;
  object-fit: cover;
  position: absolute;
  left: 25%;
  top: 40%;
}

.overlay-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

/* Top Section */
.top-section {
  position: absolute;
  top: 8%;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  max-width: 800px;
  width: 90%;
}

.solution-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

.main-title {
  font-size: 2.2rem;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 16px;
  color: white;
}

.subtitle {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 30px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.btn-smart,
.btn-precise {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 25px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-smart {
  background: white;
  color: #333;
}

.btn-precise {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* Cards */
.card {
  position: absolute;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.card-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.red-icon {
  background: #e74c3c;
}

.green-icon {
  background: #27ae60;
}

.card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.card-subtitle {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.card-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-item {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}



/* Card Positions */
.top-left-card {
  top: 32%;
  left: 12%;
}

.bottom-left-card {
  bottom: 5%;
  left: 8%;
}

.top-right-card {
  top: 28%;
  right: 8%;
}

/* Text and Features Container */
.text-features-container {
  background-image: url("/public/images/有一家设计素材铺淘宝店 <EMAIL>");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 6rem 0;
}

/* Text Section */
.text-section {
  padding-top: 2rem;
  text-align: center;
}

.text-badge {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.text-badge-content {
  background: rgba(255, 255, 255, 0.9);
  color: #666666;
  font-size: 14px;
  padding: 12px 24px;
  border-radius: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.main-text {
  font-size: 1.8rem;
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 400;
  max-width: 800px;
  margin: 0 auto;
}

/* Features Section */
.features-section {
  padding: 4rem 8% 2rem 8%;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: #ffffff;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #ffffff;
  margin: 0 auto 1.5rem;
}



.feature-card h3 {
  font-size: 14px;
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 400;
}

/* Monitoring Section Redesigned */
.monitoring-section-redesigned {
  position: relative;
  background-image: url("/public/images/<EMAIL>");
  background-size: 100% 100%;
  background-position: center center;
  background-repeat: no-repeat;
  min-height: 170vh;
  height: auto;
  padding: 80px 0 0 0;
  margin-bottom: -150px;
  z-index: 2;
  overflow: visible;
}

.curved-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: inherit;
}

.curve-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: #ecf0f1;
  clip-path: ellipse(100% 100% at 50% 0%);
  z-index: 1;
}

.curve-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: #ecf0f1;
  clip-path: ellipse(100% 100% at 50% 100%);
  z-index: 1;
}

.monitoring-content-new {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  min-height: 140vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-bottom: 4rem;
  color: #ffffff;
}

.section-header {
  text-align: center;
  margin-bottom: 1.5rem;
  padding-top: 1rem;
}

.monitoring-title-new {
  font-size: 3rem;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 0.8rem;
  color: white;
  letter-spacing: 1px;
}

.monitoring-subtitle-new {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0;
  font-weight: 300;
}

.monitoring-tabs-new {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0 2.5rem 0;
  flex-wrap: wrap;
  position: relative;
}

.monitoring-tabs-new::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.tab-btn-new {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 12px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  border-bottom: 2px solid transparent;
}

.tab-btn-new.active {
  color: #e74c3c;
  border-bottom-color: #e74c3c;
}

.tab-btn-new:hover {
  color: #e74c3c;
}

.monitoring-display-new {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: flex-start;
  max-width: 1200px;
  margin: 1.5rem auto 0;
  padding: 0 2rem;
}

.monitoring-info-new h3 {
  font-size: 1.6rem;
  margin-bottom: 1.5rem;
  color: white;
  font-weight: 600;
}

.monitoring-info-new p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.8;
  margin-bottom: 0;
  font-size: 14px;
}

.monitoring-stats-new {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  margin-top: 0;
  text-align: right;
  align-items: flex-end;
}

.stat-item-new {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.1rem 0;
  justify-content: flex-end;
}

.stat-number-new {
  font-size: 2.2rem;
  font-weight: bold;
  color: #e74c3c;
  min-width: 60px;
}

.stat-label-new {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.2;
}

/* 电脑图片展示区域样式 */
.computer-display-container {
  width: 100%;
  max-width: 1200px;
  margin: 2rem auto 0;
  padding: 0 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  gap: 1.5rem;
}

.computer-image-wrapper {
  position: relative;
  width: 100%;
  max-width: 800px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.computer-frame {
  width: 100%;
  height: auto;
  max-width: 800px;
  object-fit: contain;
  position: relative;
  z-index: 2;
}

.computer-screen {
  position: absolute;
  top: 10%;
  left: 12%;
  width: 76%;
  height: 66%;
  overflow: hidden;
  border-radius: 6px;
  z-index: 1;
}

.computer-content {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* 指示点样式 */
.computer-indicators {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.indicator-oval {
  width: 20px;
  height: 8px;
  border-radius: 4px;
  background-color: #ff4444;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ff4444;
  cursor: pointer;
  transition: all 0.3s ease;
}

.laptop-mockup-new {
  position: relative;
  perspective: 1000px;
}

.laptop-screen-new {
  width: 600px;
  height: 380px;
  background: #1a1a1a;
  border-radius: 12px 12px 0 0;
  border: 8px solid #2c3e50;
  border-bottom: none;
  transform: rotateX(8deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  overflow: hidden;
}

.screen-image-new {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.laptop-base-new {
  width: 620px;
  height: 20px;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  border-radius: 0 0 20px 20px;
  position: absolute;
  top: 380px;
  left: -10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.laptop-base-new::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 4px;
  background: #7f8c8d;
  border-radius: 2px;
}

.laptop-dots-new {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 2rem;
}

.dot-new {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: background-color 0.3s;
}

.dot-new.active {
  background: #e74c3c;
}

/* Scenarios Section */
.scenarios-section {
  position: relative;
  background-image: url("/public/images/有一家设计素材铺淘宝店 <EMAIL>");
  /* 让背景图片居中显示 */
  background-position: center;
  padding: 200px 8% 8rem 8%;
  z-index: 1;
}

.scenarios-title {
  font-size: 2.5rem;
  font-weight: 500;
  text-align: center;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.scenarios-subtitle {
  font-size: 1rem;
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 4rem;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.scenario-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  height: 240px;
  display: flex;
  flex-direction: column;
}

.scenario-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

/* 场景卡片悬浮通用样式 */
.scenario-card .hover-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
              url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop') center/cover;
  opacity: 0;
  transition: opacity 0.4s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
}

.scenario-card:hover .hover-content {
  opacity: 1;
}

.hover-overlay {
  text-align: center;
  color: white;
  padding: 2rem;
  max-width: 300px;
}

.hover-overlay h3 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: inline-block;
}

.hover-overlay p {
  font-size: 15px;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 400;
}

.scenario-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.scenario-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: #333333;
  line-height: 1.4;
}

.scenario-content p {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 1.2rem;
  flex: 1;
}

.scenario-tags {
  display: flex;
  gap: 0.6rem;
  margin-top: auto;
}

.tag {
  background: #f5f5f5;
  color: #666666;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

/* Footer */
.footer {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 4rem 8% 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 4rem;
}

.footer-logo img {
  height: 30px;
  margin-bottom: 1rem;
}

.footer-desc {
  color: #bdc3c7;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: background-color 0.3s;
}

.social-link:hover {
  background: #e74c3c;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.link-group h4 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 16px;
}

.link-group a {
  display: block;
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  margin-bottom: 0.5rem;
  transition: color 0.3s;
}

.link-group a:hover {
  color: #e74c3c;
}

.footer-contact h4 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 14px;
  color: #bdc3c7;
}

.contact-icon {
  font-size: 16px;
}

.footer-bottom {
  max-width: 1200px;
  margin: 2rem auto 0;
  padding-top: 2rem;
  border-top: 1px solid #34495e;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  color: #bdc3c7;
  font-size: 14px;
}

.footer-bottom-links {
  display: flex;
  gap: 2rem;
}

.footer-bottom-links a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.footer-bottom-links a:hover {
  color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-title {
    font-size: 1.8rem;
  }
  
  .top-left-card {
    left: 8%;
  }
  
  .bottom-left-card {
    left: 5%;
  }
  
  .top-right-card {
    right: 5%;
  }

  .monitoring-display-new {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .laptop-screen-new {
    width: 500px;
    height: 320px;
  }
}

@media (max-width: 1024px) {
  .header-content {
    padding: 0 1rem;
  }

  .nav {
    gap: 1rem;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .scenario-card {
    height: 220px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 1rem;
  }

  .nav {
    margin: 1rem 0;
  }

  .header-right {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .hero-section {
    padding: 0 5%;
    margin-top: 120px;
  }

  .hero-title {
    font-size: 1.2rem;
  }

  .text-features-container {
    padding: 4rem 0;
  }

  .text-section,
  .features-section,
  .scenarios-section {
    padding: 2rem 5%;
  }

  .main-text {
    font-size: 1.3rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .monitoring-title-new {
    font-size: 2.2rem;
  }

  .monitoring-tabs-new {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .laptop-screen-new {
    width: 350px;
    height: 220px;
  }

  .monitoring-stats-new {
    gap: 0.5rem;
  }

  .computer-display-container {
    margin: 2rem auto 0;
    padding: 0 1rem;
    min-height: 300px;
  }

  .computer-image-wrapper {
    max-width: 100%;
  }

  .computer-image {
    max-width: 100%;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .scenario-card {
    height: 200px;
  }

  .scenario-content {
    padding: 1rem;
  }

  .scenario-content h3 {
    font-size: 1.1rem;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  /* Redesigned section mobile */
  .top-section {
    top: 5%;
    width: 95%;
  }
  
  .main-title {
    font-size: 1.4rem;
  }
  
  .subtitle {
    font-size: 0.9rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  
  .card {
    min-width: 160px;
    padding: 16px;
  }
  
  .top-left-card {
    top: 45%;
    left: 5%;
  }
  
  .bottom-left-card {
    bottom: 15%;
    left: 5%;
  }
  
  .top-right-card {
    top: 45%;
    right: 5%;
  }

}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1rem;
  }

  .main-text {
    font-size: 1.1rem;
  }

  .monitoring-title-new {
    font-size: 1.8rem;
  }

  .scenarios-title {
    font-size: 1.5rem;
  }

  .laptop-screen-new {
    width: 280px;
    height: 180px;
  }

  .monitoring-tabs-new {
    flex-direction: column;
    align-items: center;
  }

  /* Redesigned section mobile */
  .monitoring-section-redesigned {
    min-height: 120vh;
    padding: 60px 0 100px 0;
    background-size: 100% 100%;
  }

  .monitoring-content-new {
    min-height: 120vh;
  }

  .main-title {
    font-size: 1.2rem;
  }
  
  .card {
    min-width: 140px;
    padding: 12px;
  }
  
  .card h3 {
    font-size: 14px;
  }
  
  .card-subtitle,
  .card-item {
    font-size: 12px;
  }
}
.left-top-image{
  position: absolute;
  top:41%;
  left:22%;
}
.left-button-image{
  position: absolute;
  top:60%;
  left:15%;
}
.left-xian-image{
  position: absolute;
  top:54%;
  right:17%;
}
</style>
import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import About from '../views/About.vue'
import Contact from '../views/Contact.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {
    path: '/contact',
    name: 'Contact',
    component: Contact
  },
  {
    path: '/product-center',
    name: 'ProductCenter',
    redirect: '/product-center/asset-management'
  },
  {
    path: '/product-center/asset-management',
    name: 'AssetManagement',
    component: () => import('../views/ProductCenter/AssetManagement.vue')
  },
  {
    path: '/product-center/power-distribution',
    name: 'PowerDistribution',
    component: () => import('../views/ProductCenter/PowerDistribution.vue')
  },
  {
    path: '/product-center/energy-management',
    name: 'EnergyManagement',
    component: () => import('../views/ProductCenter/EnergyManagement.vue')
  },
  {
    path: '/industry-solution',
    name: 'IndustrySolution',
    redirect: '/industry-solution/smart-microgrid'
  },
  {
    path: '/industry-solution/smart-microgrid',
    name: 'SmartMicrogrid',
    component: () => import('../views/IndustrySolution/SmartMicrogrid.vue')
  },
  {
    path: '/industry-solution/power-monitoring',
    name: 'PowerMonitoring',
    component: () => import('../views/IndustrySolution/PowerMonitoring.vue')
  },
  {
    path: '/industry-solution/residential-energy',
    name: 'ResidentialEnergy',
    component: () => import('../views/IndustrySolution/ResidentialEnergy.vue')
  },
  {
    path: '/industry-solution/data-center',
    name: 'DataCenter',
    component: () => import('../views/IndustrySolution/DataCenter.vue')
  },
  {
    path: '/industry-solution/building-control',
    name: 'BuildingControl',
    component: () => import('../views/IndustrySolution/BuildingControl.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router

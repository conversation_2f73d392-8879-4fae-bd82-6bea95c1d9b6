<template>
  <div class="contact">
    <!-- Contact Content Section -->
    <section class="contact-content">
      <div class="content-container">
        <div class="contact-grid">
          <!-- Contact Info -->
          <div class="contact-info">
            <h1 class="hero-title">Contact Us</h1>
            <p class="hero-subtitle">
              CGC DIGITAL provides a variety of<br />
              services covering the lifecycle of your<br />
              project
            </p>
            <hr />
            <div class="office-info">
              <div class="office-address">
                <p>Head office</p>
                <p>A15 Building</p>
                <p>74 Ba Trieu St, Hoan Kiem Dist.</p>
                <p><PERSON><PERSON>, Vietnam</p>
              </div>
              <div class="office-contact">
                <p>T: +84-24-3825 1072F +84-24-3826 8037</p>
              </div>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="contact-form">
            <div class="form-container">
              <h2 class="form-title">
                We Look Forward to Hearing <br />
                From You
              </h2>
              <p class="form-subtitle">
                Please send us your inquiries in form below or email us at
                ******@outlook.com
              </p>

              <form @submit.prevent="submitForm">
                <div class="form-row">
                  <div class="form-group">
                    <label for="firstName">Name (required)</label>
                    <input
                      type="text"
                      id="firstName"
                      v-model="form.firstName"
                      placeholder="First Name"
                      required
                    />
                  </div>
                  <div class="form-group">
                    <input
                      type="text"
                      id="lastName"
                      v-model="form.lastName"
                      placeholder="Last Name"
                    />
                  </div>
                </div>

                <div class="form-group">
                  <label for="email">Email (required)</label>
                  <input
                    type="email"
                    id="email"
                    v-model="form.email"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="phone">Phone</label>
                  <input type="tel" id="phone" v-model="form.phone" />
                </div>

                <div class="form-group">
                  <label for="organization">Organization</label>
                  <input
                    type="text"
                    id="organization"
                    v-model="form.organization"
                  />
                </div>

                <div class="form-group">
                  <label for="region">Your region (required)</label>
                  <input
                    type="text"
                    id="region"
                    v-model="form.region"
                    required
                  />
                </div>

                <button type="submit" class="submit-btn">Send</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";

const form = reactive({
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  organization: "",
  region: "",
});

const submitForm = () => {
  // Handle form submission
  console.log("Form submitted:", form);
  alert("Message sent successfully! We will get back to you soon.");

  // Reset form
  Object.keys(form).forEach((key) => {
    form[key as keyof typeof form] = "";
  });
};
</script>

<style scoped>
.contact {
  padding-top: 80px;
  min-height: 100vh;
  background-image: url("/images/contactbg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: white;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */

.hero-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 1rem;
  color: #ffffff;
  opacity: 0.8;
  line-height: 1.6;
  margin-bottom: 30px;
}

/* Contact Content Section */
.contact-content {
  padding: 60px 0;
}

.contact-grid {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 80px;
  align-items: start;
}

/* Contact Info */
.contact-info {
  color: white;
}
.office-info {
  margin-top: 40px;
  line-height: 1.6;
}

.office-address p {
  margin-bottom: 5px;
  color: #ffffff;
  opacity: 0.9;
}

.office-contact {
  margin-top: 20px;
}

.office-contact p {
  color: #ffffff;
  opacity: 0.9;
}

/* Contact Form */
.contact-form {
  background: #ffffff;
  border-radius: 15px;
  padding: 40px;
  color: #333;
}

.form-container {
  max-width: 100%;
}

.form-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.2;
  text-align: center;
}

.form-subtitle {
  color: #666;
  margin-bottom: 30px;
  text-align: center;
  line-height: 1.5;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
  align-items: end;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 0.95rem;
  background: #f8f9fa;
  transition: border-color 0.3s, background-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #4c6ef5;
  background: #ffffff;
}

.form-group input::placeholder {
  color: #999;
}

.submit-btn {
  background: linear-gradient(253deg, #ca2a22 1%, #a5160f 97%);
  color: white;
  border: none;
  padding: 12px 80px;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
  display: block;
  margin: 30px auto 0;
}
/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.2rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }

  .contact-form {
    padding: 30px 20px;
  }

  .form-title {
    font-size: 1.5rem;
  }
}
</style>

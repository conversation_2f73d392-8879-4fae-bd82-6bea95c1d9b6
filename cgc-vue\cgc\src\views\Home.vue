<template>
  <div class="home">
    <!-- Hero Section -->
    <section class="hero">
      <el-carousel class="hero-background" arrow="never" :interval="3000">
        <el-carousel-item v-for="image in images" :key="image.src">
          <img :src="image.src" alt="Background" class="hero-bg-image" />
        </el-carousel-item>
      </el-carousel>
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            CGC DIGITAL<br />
            provides a variety of<br />
            services covering the<br />
            lifecycle of your<br />
            project
          </h1>
          <p class="hero-subtitle">
            A full process solution covering energy management, power
            monitoring, and asset management
          </p>
        </div>
      </div>
    </section>

    <!-- Clients Section -->
    <section class="clients">
      <div class="content-container">
        <h2 class="clients-title">OUR CLIENTS</h2>
        <div class="clients-logos">
          <div class="client-logo">
            <img src="/images/clients/1.png" alt="Client 1" />
          </div>
          <div class="client-logo">
            <img src="/images/clients/2.png" alt="Client 2" />
          </div>
          <div class="client-logo">
            <img src="/images/clients/3.png" alt="Client 3" />
          </div>
          <div class="client-logo">
            <img src="/images/clients/4.png" alt="Client 4" />
          </div>
          <div class="client-logo">
            <img src="/images/clients/5.png" alt="Client 5" />
          </div>
          <div class="client-logo">
            <img src="/images/clients/6.png" alt="Client 6" />
          </div>
          <div class="client-logo">
            <img src="/images/clients/7.png" alt="Client 7" />
          </div>
          <div class="client-logo">
            <img src="/images/clients/8.png" alt="Client 8" />
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section class="services">
      <div class="content-container">
        <div class="services-content">
          <!-- Left Side - Service Cards in 2+1 Layout -->
          <div class="services-left">
            <el-carousel
              class="services-left-carousel"
              height="100vh"
              width="100%"
              arrow="never"
              :interval="2000"
              :autoplay="true"
              indicator-position="none"
              @change="handleCarouselChange"
            >
              <el-carousel-item>
                <div class="services-content-wrapper">
                  <!-- First Column - 2 Cards -->
                  <div class="services-column-1">
                    <!-- Service Card 1 -->
                    <div class="service-card">
                      <div class="service-icon">
                        <img src="/images/indexIcon/s1.png" alt="" />
                      </div>
                      <h3 class="service-title">智能变电站自动化系统</h3>
                      <div class="service-tags">
                        <span class="service-tag">集成控制保护</span>
                        <span class="service-tag">多厂商兼容</span>
                        <span class="service-tag">三维映射</span>
                      </div>
                      <p class="service-description">
                        基于硬件到软件的全链路数字孪生架构，构建变电站三维动态镜像。实时映射设备状态、负荷分布及故障点，支持多厂商保护控制系统协同。
                      </p>
                    </div>

                    <!-- Service Card 3 -->
                    <div class="service-card">
                      <div class="service-icon">
                        <img src="/images/indexIcon/s2.png" alt="" />
                        <svg
                          width="60"
                          height="60"
                          viewBox="0 0 60 60"
                          fill="none"
                        >
                          <rect
                            x="10"
                            y="15"
                            width="40"
                            height="30"
                            rx="4"
                            stroke="white"
                            stroke-width="2"
                            fill="none"
                          />
                          <path
                            d="M15 25H45M15 30H45M15 35H45"
                            stroke="white"
                            stroke-width="1.5"
                          />
                          <circle cx="20" cy="25" r="2" fill="white" />
                          <circle cx="20" cy="30" r="2" fill="white" />
                          <circle cx="20" cy="35" r="2" fill="white" />
                        </svg>
                      </div>
                      <h3 class="service-title">电力SCADA与智能监控平台</h3>
                      <div class="service-tags">
                        <span class="service-tag">全景可视化</span>
                        <span class="service-tag">AI预警</span>
                        <span class="service-tag">拓扑仿真</span>
                      </div>
                      <p class="service-description">
                        深度融合三维电网模型与实时SCADA数据，实现输配电路径动态仿真。AI驱动设备劣化预警与能效优化，降低非计划停机风险，提升调度响应速度。
                      </p>
                    </div>
                  </div>

                  <!-- Second Column - 1 Card (Centered) -->
                  <div class="services-column-2">
                    <!-- Service Card 2 -->
                    <div class="service-card">
                      <div class="service-icon">
                        <img src="/images/indexIcon/s3.png" alt="" />
                      </div>
                      <h3 class="service-title">光伏电站孪生控制系统</h3>
                      <div class="service-tags">
                        <span class="service-tag">PPC调优</span>
                        <span class="service-tag">三维辐照仿真</span>
                        <span class="service-tag">发电量优化</span>
                      </div>
                      <p class="service-description">
                        专为大型地面电站与分布式光伏设计的全生命周期监控方案。通过三维环境建模精准模拟光伏流向，监控发电状态，降低运维成本，确保电站收益最大化与电网兼容性。
                      </p>
                    </div>
                  </div>
                </div>
              </el-carousel-item>

              <!-- Second Slide: Swapped Layout -->
              <el-carousel-item>
                <div class="services-content-wrapper">
                  <!-- Swapped: Second Column (now first) -->
                  <div class="services-column-2">
                    <!-- Service Card 2 -->
                    <div class="service-card">
                      <div class="service-icon">
                        <img src="/images/indexIcon/s3.png" alt="" />
                      </div>
                      <h3 class="service-title">光伏电站孪生控制系统</h3>
                      <div class="service-tags">
                        <span class="service-tag">PPC调优</span>
                        <span class="service-tag">三维辐照仿真</span>
                        <span class="service-tag">发电量优化</span>
                      </div>
                      <p class="service-description">
                        专为大型地面电站与分布式光伏设计的全生命周期监控方案。通过三维环境建模精准模拟光伏流向，监控发电状态，降低运维成本，确保电站收益最大化与电网兼容性。
                      </p>
                    </div>
                  </div>

                  <!-- Swapped: First Column (now second) -->
                  <div class="services-column-1">
                    <!-- Service Card 1 -->
                    <div class="service-card">
                      <div class="service-icon">
                        <img src="/images/indexIcon/s1.png" alt="" />
                      </div>
                      <h3 class="service-title">智能变电站自动化系统</h3>
                      <div class="service-tags">
                        <span class="service-tag">集成控制保护</span>
                        <span class="service-tag">多厂商兼容</span>
                        <span class="service-tag">三维映射</span>
                      </div>
                      <p class="service-description">
                        基于硬件到软件的全链路数字孪生架构，构建变电站三维动态镜像。实时映射设备状态、负荷分布及故障点，支持多厂商保护控制系统协同。
                      </p>
                    </div>

                    <!-- Service Card 3 -->
                    <div class="service-card">
                      <div class="service-icon">
                        <img src="/images/indexIcon/s2.png" alt="" />
                        <svg
                          width="60"
                          height="60"
                          viewBox="0 0 60 60"
                          fill="none"
                        >
                          <rect
                            x="10"
                            y="15"
                            width="40"
                            height="30"
                            rx="4"
                            stroke="white"
                            stroke-width="2"
                            fill="none"
                          />
                          <path
                            d="M15 25H45M15 30H45M15 35H45"
                            stroke="white"
                            stroke-width="1.5"
                          />
                          <circle cx="20" cy="25" r="2" fill="white" />
                          <circle cx="20" cy="30" r="2" fill="white" />
                          <circle cx="20" cy="35" r="2" fill="white" />
                        </svg>
                      </div>
                      <h3 class="service-title">电力SCADA与智能监控平台</h3>
                      <div class="service-tags">
                        <span class="service-tag">全景可视化</span>
                        <span class="service-tag">AI预警</span>
                        <span class="service-tag">拓扑仿真</span>
                      </div>
                      <p class="service-description">
                        深度融合三维电网模型与实时SCADA数据，实现输配电路径动态仿真。AI驱动设备劣化预警与能效优化，降低非计划停机风险，提升调度响应速度。
                      </p>
                    </div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>

            <!-- Custom Indicator -->
            <div class="services-indicator">
              <div
                class="indicator-dot"
                :class="{ active: currentSlide === 0 }"
              ></div>
              <div
                class="indicator-dot"
                :class="{ active: currentSlide === 1 }"
              ></div>
            </div>
          </div>

          <!-- Right Side - Featured Solutions -->
          <div class="services-right">
            <div class="featured-content">
              <img
                src="/images/Vector.png"
                alt="Vector decoration"
                class="featured-vector"
              />
              <h2 class="featured-title">FEATURED<br />SOLUTIONS</h2>
              <p class="featured-description">
                Cryptally has a variety of features that make it<br />
                the best place to start trading
              </p>
              <button class="featured-btn">Learn More</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Global Platform Section -->
    <section class="global-platform">
      <div class="content-container">
        <div class="platform-content">
          <div class="platform-text">
            <h2 class="platform-title">
              We are the most trustworthy full<br />
              cycle digital service platform.
            </h2>
            <p class="platform-description">
              Full Link 3D Twin Platform | Accurate Equipment Mapping, Fault
              Rehearsal Decision Making, Energy Efficiency Dynamic Optimization
            </p>
            <div class="platform-features">
              <div class="feature-item">
                <div class="feature-icon">
                  <div class="icon-circle">
                    <img src="/images/indexIcon/w1.png" alt="" />
                  </div>
                </div>
                <div class="feature-text">
                  <h3>全域三维透视</h3>
                  <p>
                    变电站设备/风机叶片/光伏组串/BIM管线毫秒级映射，穿透硬件黑箱
                  </p>
                </div>
              </div>
              <div class="feature-item">
                <div class="feature-icon">
                  <div class="icon-circle">
                    <img src="/images/indexIcon/w2.png" alt="" />
                  </div>
                </div>
                <div class="feature-text">
                  <h3>跨域协同智控</h3>
                  <p>
                    联动保护装置、AGC调频、楼宇HVAC，打破系统孤岛，响应速度提升90%
                  </p>
                </div>
              </div>
              <div class="feature-item">
                <div class="feature-icon">
                  <div class="icon-circle">
                    <img src="/images/indexIcon/w3.png" alt="" />
                  </div>
                </div>
                <div class="feature-text">
                  <h3>预演决策闭环</h3>
                  <p>
                    基于实时数据仿真电网故障、阴影遮挡、尾流效应，预优化策略降损30%+
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="platform-image">
            <div class="world-map-container">
              <svg class="dashed-rect-svg" width="100%" height="100%">
                <rect x="5%" y="5%" width="90%" height="90%" rx="0" ry="0" />
              </svg>
              <svg class="dashed-ellipse-svg" width="100%" height="100%">
                <defs>
                  <!-- Motion path for icons, matching the ellipse -->
                  <ellipse
                    id="orbit-path"
                    cx="50%"
                    cy="50%"
                    rx="55%"
                    ry="30%"
                    transform="rotate(-30, 250, 250)"
                  />

                  <!-- 使用百分比坐标，0→1 对应左→右 -->
                  <linearGradient
                    id="ellipseBorderGradient"
                    gradientUnits="objectBoundingBox"
                    x1="0"
                    y1="0.5"
                    x2="1"
                    y2="0.5"
                    gradientTransform="rotate(-30 .5 .5)"
                  >
                    <!-- 左侧渐隐藏（更贴近当前虚线节奏：0-10%透明，14%开始显现，22%达满） -->
                    <stop offset="0%" stop-color="#34384C" stop-opacity="0" />
                    <stop offset="10%" stop-color="#34384C" stop-opacity="0" />
                    <stop
                      offset="14%"
                      stop-color="#34384C"
                      stop-opacity="0.35"
                    />
                    <stop
                      offset="18%"
                      stop-color="#34384C"
                      stop-opacity="0.7"
                    />
                    <stop offset="22%" stop-color="#34384C" stop-opacity="1" />
                    <!-- 右侧保持实色，不再渐变 -->
                    <stop offset="100%" stop-color="#34384c" stop-opacity="1" />
                  </linearGradient>
                </defs>

                <ellipse
                  cx="50%"
                  cy="50%"
                  rx="55%"
                  ry="30%"
                  fill="none"
                  stroke="url(#ellipseBorderGradient)"
                  stroke-width="2"
                  stroke-dasharray="4 4"
                />
              </svg>
              <img src="/images/地球@2x.png" alt="Global Platform" />

              <!-- Icons as SVG foreignObjects for path animation -->
              <g class="orbit-icons-group">
                <!-- Green Icon -->
                <foreignObject
                  class="orbit-icon icon-green"
                  x="-25"
                  y="-25"
                  width="50"
                  height="50"
                >
                  <i class="fas fa-handshake"></i>
                  <animateMotion
                    dur="12s"
                    repeatCount="indefinite"
                    rotate="auto"
                    keyPoints="0;1"
                    keyTimes="0;1"
                    calcMode="linear"
                  >
                    <mpath xlink:href="#orbit-path" />
                  </animateMotion>
                </foreignObject>

                <!-- Blue Icon -->
                <foreignObject
                  class="orbit-icon icon-blue"
                  x="-25"
                  y="-25"
                  width="50"
                  height="50"
                >
                  <i class="fas fa-chart-line"></i>
                  <animateMotion
                    dur="12s"
                    repeatCount="indefinite"
                    rotate="auto"
                    keyPoints="0.33;1;0;0.33"
                    keyTimes="0;0.67;0.67;1"
                    calcMode="linear"
                  >
                    <mpath xlink:href="#orbit-path" />
                  </animateMotion>
                </foreignObject>

                <!-- Red Icon -->
                <foreignObject
                  class="orbit-icon icon-red"
                  x="-25"
                  y="-25"
                  width="50"
                  height="50"
                >
                  <i class="fas fa-sync-alt"></i>
                  <animateMotion
                    dur="12s"
                    repeatCount="indefinite"
                    rotate="auto"
                    keyPoints="0.66;1;0;0.66"
                    keyTimes="0;0.34;0.34;1"
                    calcMode="linear"
                  >
                    <mpath xlink:href="#orbit-path" />
                  </animateMotion>
                </foreignObject>
              </g>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Projects Grid Section -->
    <section class="projects-grid">
      <div class="content-container">
        <div class="projects-layout">
          <div class="project-card">
            <div class="project-icon">
              <img src="/images/indexIcon/p1.png" alt="Investors" />
            </div>
            <div class="project-content">
              <h3>Investors 投资者</h3>
              <p>
                Oversee portfolios and maximize returns on renewable
                energyinvestments.
              </p>
            </div>
          </div>
          <div class="project-card">
            <div class="project-icon">
              <img src="/images/indexIcon/p2.png" alt="Developers" />
            </div>
            <div class="project-content">
              <h3>Developers 开发人员</h3>
              <p>
                Identify, plan, and initiate early-stage renewable energy
                projects
              </p>
            </div>
          </div>
          <div class="project-card">
            <div class="project-icon">
              <img src="/images/indexIcon/p3.png" alt="Developers" />
            </div>
            <div class="project-content">
              <h3>Independent power producers</h3>
              <p>
                Drive energy market supply throughinnovative development and
                efficientoperations.
              </p>
            </div>
          </div>
          <div class="project-card">
            <div class="project-icon">
              <img src="/images/indexIcon/p4.png" alt="Developers" />
            </div>
            <div class="project-content">
              <h3>Independent service providers</h3>
              <p>通过专业的资产管理和运维服务优化资产性能。</p>
            </div>
          </div>
          <div class="project-card">
            <div class="project-icon">
              <img src="/images/indexIcon/p5.png" alt="Developers" />
            </div>
            <div class="project-content">
              <h3>Engineering, procurement & construction</h3>
              <p>
                通过专业的设计、采购和施工服务，交付高质量的可再生能源项目。
              </p>
            </div>
          </div>
          <div class="project-card">
            <div class="project-icon">
              <img src="/images/indexIcon/p6.png" alt="Developers" />
            </div>
            <div class="project-content">
              <h3>Utilities 公用事业公司</h3>
              <p>
                Ensure reliable energy generation.transmission, and distribution
                forcommunities and industries
              </p>
            </div>
          </div>
          <div class="project-card">
            <div class="project-icon">
              <img src="/images/indexIcon/p7.png" alt="Developers" />
            </div>
            <div class="project-content">
              <h3>Corporate offtakers企业offtakers</h3>
              <p>
                Secure clean energy solutions to meetindustrial demands and
                achieve net zerotargets.
              </p>
            </div>
          </div>
          <div class="project-card">
            <div class="project-icon">
              <img src="/images/indexIcon/p8.png" alt="Developers" />
            </div>
            <div class="project-content">
              <h3>Transitioning oil & gas producers</h3>
              <p>向可再生能源转型，扩大可持续资产组合和业务多样性。</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CGC Digital Company Section -->
    <section class="cgc-digital">
      <div class="content-container">
        <div class="cgc-header">
          <h2 class="cgc-title">
            CGC DIGITAL IS A GLOBAL ENGINEERING<br />
            COMPANY SPECIALIZING IN
          </h2>
        </div>
        <div class="cgc-services">
          <div class="service-item">
            <div class="service-image">
              <img src="/images/圆1.png" alt="Systems Integration" />
            </div>
            <div class="service-content">
              <h3>SYSTEMS INTEGRATION</h3>
              <p>
                We design, supply, deploy and commission energy managementistems
                for renewables, and electrifcation and energy
                effciencyinitiatives.
              </p>
            </div>
          </div>
          <div class="service-item">
            <div class="service-image">
              <img src="/images/圆2.png" alt="Energy IoT" />
            </div>
            <div class="service-content">
              <h3>ENERGY IOT</h3>
              <p>
                We create and operate loT applications, connecting customer
                sites to our cloud seryices with secure networks
              </p>
            </div>
          </div>
          <div class="service-item">
            <div class="service-image">
              <img src="/images/圆3.png" alt="Lifecycle Services" />
            </div>
            <div class="service-content">
              <h3>LIFECYCLE SERVICES</h3>
              <p>
                We provide support seryices,professional services. and
                managedservices for complete coverage over the lifetime of your
                system.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Standards Section -->
    <section class="standards">
      <div class="content-container">
        <div class="standards-content">
          <h2 class="standards-title">
            We always follow the standard <br />work process
          </h2>
          <p class="standards-subtitle">
            从蓝图到碳足迹的AI闭环：让每度电的生命周期价值提升
          </p>
          <div class="standards-grid">
            <div class="standard-item">
              <div class="standard-number">1</div>
              <h3>咨询规划</h3>
              <p>
                基于LSTM负荷预测与动态碳模型，定制十年低碳路线图，规避过度投资风险。
              </p>
              <img
                src="/images/line.png"
                alt="connection line"
                class="connection-line line-1-2"
              />
            </div>
            <div class="standard-item">
              <div class="standard-number">2</div>
              <h3>设计实施</h3>
              <p>数字孪生驱动智能选型与容器化部署，确保系统耐受性提升</p>
              <img
                src="/images/line.png"
                alt="connection line"
                class="connection-line line-2-3"
              />
            </div>
            <div class="standard-item">
              <div class="standard-number">3</div>
              <h3>测试交付</h3>
              <p>三级压力测试（过载/逻辑/物理）交付数字资产包</p>
              <img
                src="/images/line.png"
                alt="connection line"
                class="connection-line line-3-4"
              />
            </div>
            <div class="standard-item">
              <div class="standard-number">4</div>
              <h3>运营维护</h3>
              <p>AI预测性维护+SLA保障，压缩故障率</p>
              <img
                src="/images/line.png"
                alt="connection line"
                class="connection-line line-4-5"
              />
            </div>
            <div class="standard-item">
              <div class="standard-number">5</div>
              <h3>能效优化</h3>
              <p>需量控制+电价套利+无功补偿三杠杆，刚性降低电费支出</p>
              <img
                src="/images/line.png"
                alt="connection line"
                class="connection-line line-5-6"
              />
            </div>
            <div class="standard-item">
              <div class="standard-number">6</div>
              <h3>改善升级</h3>
              <p>旧设备智能改造+碳资产激活</p>
              <img
                src="/images/line.png"
                alt="connection line"
                class="connection-line line-6-1"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <!-- <Footer /> -->
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
// import Footer from "@/components/Footer.vue";

const images = ref([
  { src: "/images/办公楼宇.jpg" },
  { src: "/images/办公写字楼.png" },
  { src: "/images/商业综合体.png" },
  { src: "/images/容器 8.png" },
]);

const currentSlide = ref(0);

const handleCarouselChange = (index: number) => {
  currentSlide.value = index;
};
</script>


<style scoped>
* {
  box-sizing: border-box;
}

html,
body {
  overflow-x: hidden;
  width: 100%;
}

.home {
  margin-top: 80px; /* Account for fixed navbar */
  width: 100%;
  overflow-x: hidden;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  background: #1e1f25;
  display: flex;
  align-items: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero :deep(.el-carousel__container) {
  height: 100%;
}
.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Styles for the indicators */
.hero :deep(.el-carousel__indicators) {
  position: absolute;
  bottom: 20px; /* Adjust this value to move indicators up/down */
  left: 50%;
  transform: translateX(-50%);
  z-index: 2; /* Ensure indicators are on top of the image */
}

.hero :deep(.el-carousel__indicator .el-carousel__button) {
  width: 12px;
  height: 12px;
  margin: 0 8px;
  border-radius: 50%;
  background: rgba(202, 42, 34, 0.4);
  opacity: 1;
  transition: all 0.3s;
}

.hero :deep(.el-carousel__indicator.is-active .el-carousel__button) {
  width: 50px;
  border-radius: 6px;
  background: #a5160f;
}

.hero-content {
  position: relative;
  z-index: 3;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  justify-content: center;
  align-items: flex-start;
}

.hero-text {
  width: 100%;
}

.hero-title {
  font-family: Poppins;
  font-size: 54px;
  font-weight: 600;
  line-height: 110%;
  letter-spacing: 0em;
  font-variation-settings: "opsz" auto;
  color: #ffffff;
  margin: 0;
}

.hero-subtitle {
  font-family: Poppins;
  font-size: 24px;
  font-weight: normal;
  line-height: 27px;
  letter-spacing: 0px;
  font-variation-settings: "opsz" auto;
  color: #e5e6ed;
  margin-top: 30px;
}

/* Clients Section */
.clients {
  padding: 80px 0;
  background: #ffffff;
}

.clients-title {
  text-align: center;
  font-size: 36px;
  font-weight: bold;
  color: #3d3d3d;
  margin-bottom: 30px;
  letter-spacing: 2px;
}

.clients-logos {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.client-logo {
  width: 161px;
  height: 78px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.client-logo img {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}

/* Services Section */
.services {
  position: relative;
  padding: 100px 0 150px 0;
  background: #1e1f25;
  color: #ffffff;
  overflow: visible;
}

.services-content {
  display: grid;
  grid-template-columns: 1.25fr 0.75fr; /* widen left column */
  gap: 60px; /* slightly smaller gap */
  align-items: flex-start;
  width: 100%;
  max-width: 100%;
}

.services-left {
  position: relative;
  padding-bottom: 50px;
}

.services-content-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: start;
}

/* Custom Services Indicator */
.services-indicator {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16px;
  align-items: center;
}

.indicator-dot {
  width: 40px;
  height: 4px;
  border-radius: 20px;
  background-color: #cccccc;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  height: 8px;
  background-color: #a5160f;
}

.services-column-1 {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.services-column-2 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.services-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  position: relative;
}

.service-card {
  background: rgba(47, 50, 65, 0.5);
  border-radius: 12px;
  width: 320px;
  padding: 50px 15px;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.service-card:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.08);
}

.service-icon {
  width: 100px;
  height: 100px;
  background: rgba(47, 50, 65, 0.5);
  border-radius: 50%;
  backdrop-filter: blur(14px);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30px auto;
}

.service-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 15px;
  line-height: 1.3;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 15px;
  justify-content: center;
}

.service-tag {
  background: none;
  border: 1px solid #881a17;
  color: #ffffff;
  padding: 3px 10px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
}

.service-description {
  text-align: left;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #a5adcf;
}

/* Featured Solutions Section */
.featured-content {
  text-align: right;
  margin-top: 250px;
  position: relative;
}
.featured-vector {
  position: absolute;
  top: 40%;
  left: -170px;
  max-width: 600px;
  height: auto;
  opacity: 0.7;
  z-index: 2;
}

.featured-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.1;
  margin-bottom: 20px;
  position: relative;
  z-index: 3;
}

.featured-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 40px;
  position: relative;
  z-index: 3;
}

.featured-btn {
  background: linear-gradient(260deg, #ca2a22 0%, #a5160f 97%);
  color: #ffffff;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 3;
}

.featured-btn:hover {
  background: #b02419;
  transform: translateX(5px);
}

.featured-btn::after {
  content: "→";
  margin-left: 10px;
  transition: transform 0.3s ease;
}

.featured-btn:hover::after {
  transform: translateX(5px);
}

.featured-decoration {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
}

/* Global Platform Section */
.global-platform {
  padding: 100px 0;
 
}

.platform-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
}

.platform-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #080712;
  line-height: 1.2;
  margin-bottom: 20px;
}

.platform-description {
  font-size: 1rem;
  color: #505360;
  line-height: 1.6;
  margin-bottom: 40px;
}

.platform-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.feature-icon {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.icon-circle {
  width: 60px;
  height: 60px;
  border-radius: 5px;
  background: rgba(219, 219, 219, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-circle i {
  color: white;
  font-size: 24px;
}

.feature-text h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e1f25;
  margin-bottom: 8px;
}

.feature-text p {
  color: #666;
  line-height: 1.5;
  font-size: 0.9rem;
}

.world-map-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 500px;
  height: 500px;
  margin: 0 auto; /* Center the container */
}

.dashed-rect-svg,
.dashed-ellipse-svg {
  position: absolute;
  top: 0;
  left: 0;
  overflow: visible;
}

/* Ensure stacking order so ellipse is visible above the image */
.dashed-rect-svg {
  z-index: 1; /* lowest: behind ellipse, image, and icons */
}
.dashed-ellipse-svg {
  z-index: 0; /* below image and orbit icons */
}

.dashed-rect-svg rect {
  fill: none;
  stroke: #cccccc;
  stroke-width: 2;
  stroke-dasharray: 10 10; /* Long dashes for rectangle */
}

.dashed-ellipse-svg ellipse {
  fill: none;
  stroke: #34384c;
  stroke-width: 2;
  stroke-dasharray: 4 4; /* Short, dot-like dashes for ellipse */
}

.dashed-ellipse-svg {
  transform: rotate(-30deg);
}

.world-map-container img {
  max-width: 80%;
  max-height: 80%;
  height: auto;
  position: relative; /* enable z-index */
  z-index: 1;
}

.orbit-icon {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 24px;
  border: 3px solid #ffffff;
  z-index: 2; /* above image and dashed ellipse */
}

.icon-green {
  background: #14b082;
  top: 50%;
  left: 0%;
}

.icon-blue {
  background: #525977;
  top: 10%;
  right: 0%;
}

.icon-red {
  background: #d7352a;
  bottom: 20%;
  left: 60%;
}

.decorative-balls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.ball {
  position: absolute;
  border-radius: 50%;
}

.ball-1 {
  width: 40px;
  height: 40px;
  background: #2196f3;
  top: 20%;
  right: 15%;
}

.ball-2 {
  width: 30px;
  height: 30px;
  background: #f44336;
  bottom: 25%;
  right: 20%;
}

/* Projects Grid Section */
.projects-grid {
  padding: 100px 0;
  background: #1e1f25;
}

.projects-layout {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(3, auto);
  gap: 20px;
}

.project-card:nth-child(1) {
  grid-column: span 3;
}

.project-card:nth-child(2) {
  grid-column: span 3;
}

.project-card:nth-child(3),
.project-card:nth-child(4),
.project-card:nth-child(5) {
  grid-column: span 2;
}

.project-card:nth-child(6),
.project-card:nth-child(7),
.project-card:nth-child(8) {
  grid-column: span 2;
}

.project-card {
  background: linear-gradient(125deg, #dedede 0%, #ffffff 59%);
  border-radius: 5px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  min-height: 180px;
}

.project-card:hover {
  transform: translateY(-5px);
}

.project-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.project-icon i {
  font-size: 24px;
  color: #333;
}

.project-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #1e1f25;
  line-height: 1.3;
}

.project-content p {
  color: #666;
  line-height: 1.5;
  font-size: 0.95rem;
}

/* CGC Digital Section */
.cgc-digital {
  padding: 100px 0;
  background: #f5f5f5;
}

.cgc-header {
  text-align: center;
  margin-bottom: 80px;
}

.cgc-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #080712;
  line-height: 1.2;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cgc-services {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.service-item {
  text-align: center;
}

.service-image {
  width: 250px;
  height: 250px;
  margin: 0 auto 30px;
  border-radius: 50%;
  overflow: hidden;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #080712;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.service-content p {
  color: #505360;
  line-height: 1.6;
  font-size: 1rem;
}

/* Services Section */
.services {
  background-color: #1e1e1e; /* Dark background */
  padding: 100px 0;
  color: #ffffff;
  position: relative;
}

/* Standards Section */
.standards {
  padding: 100px 0;
  background: #1e1f25;
  color: #ffffff;
  position: relative;
}

.standards::before {
  content: "";
  position: absolute;
  top: -50px;
  left: 0;
  right: 0;
  height: 100px;
  background: #1e1f25;
  border-radius: 50% 50% 0 0 / 100% 100% 0 0;
  transform: scaleX(2);
}

.standards-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.standards-subtitle {
  text-align: center;
  font-size: 1.1rem;
  font-weight: 300;
  color: #ffffff;
  margin-bottom: 80px;
  line-height: 1.6;
}

.standards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 60px;
}

.standard-item {
  padding: 0px 60px;
  text-align: center;
  position: relative;
}

.standard-number {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #ffffff;
  color: #00d4aa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto 20px;
}

.standard-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffffff;
}

.standard-item p {
  color: #ffffff;
  opacity: 0.8;
  line-height: 1.6;
}

/* Connection Lines */
.connection-line {
  position: absolute;
  width: 120px;
  height: auto;
  z-index: 1;
  opacity: 0.8;
}

/* Line from 1 to 2 (horizontal right) */
.line-1-2 {
  top: 50%;
  right: -60px;
  transform: translateY(-50%);
}

/* Line from 2 to 3 (horizontal right) */
.line-2-3 {
  top: 50%;
  right: -60px;
  transform: translateY(-50%) scaleY(-1);
}

/* Line from 3 to 4 (curved down) */
.line-3-4 {
  bottom: -60px;
  right: 0%;
  transform: translateX(50%) rotate(-90deg) scaleX(-1);
}

/* Line from 4 to 5 (horizontal left) */
.line-4-5 {
  top: 0%;
  left: -60px;
  transform: translateY(-50%) rotate(-90deg) scaleY(-1);
}

/* Line from 5 to 6 (horizontal left) */
.line-5-6 {
  top: 50%;
  left: -60px;
  transform: translateY(-50%) rotate(180deg);
}

/* Line from 6 to 1 (curved up) */
.line-6-1 {
  top: 50%;
  left: 0%;
  transform: translateX(-50%) rotate(180deg) scaleY(-1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .platform-content {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .projects-layout {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, auto);
  }

  .project-card:nth-child(1),
  .project-card:nth-child(2) {
    grid-column: span 2;
  }

  .project-card:nth-child(3),
  .project-card:nth-child(4),
  .project-card:nth-child(5),
  .project-card:nth-child(6),
  .project-card:nth-child(7),
  .project-card:nth-child(8) {
    grid-column: span 2;
  }

  .cgc-services {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .standards-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 40px;
  }
}

@media (max-width: 768px) {
  * {
    max-width: 100%;
    overflow-x: hidden;
  }

  .hero-content {
    max-width: calc(100% - 40px);
    padding: 0 20px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 40px;
    line-height: 110%;
  }

  .hero-subtitle {
    font-size: 18px;
    line-height: 20px;
  }

  .platform-title {
    font-size: 2rem;
  }

  .platform-features {
    gap: 20px;
  }

  .projects-layout {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(8, auto);
    gap: 15px;
  }

  .project-card:nth-child(1),
  .project-card:nth-child(2),
  .project-card:nth-child(3),
  .project-card:nth-child(4),
  .project-card:nth-child(5),
  .project-card:nth-child(6),
  .project-card:nth-child(7),
  .project-card:nth-child(8) {
    grid-column: span 1;
  }

  .cgc-title {
    font-size: 1.8rem;
  }

  .services-content {
    grid-template-columns: 1fr;
    gap: 40px;
    width: 100%;
    overflow-x: hidden;
  }

  .services-left {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .services-column-1,
  .services-column-2 {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .services-column-2 {
    justify-content: flex-start;
    align-items: stretch;
    height: auto;
  }

  .service-card {
    padding: 25px 20px;
  }

  .service-title {
    font-size: 1.1rem;
  }

  .featured-title {
    font-size: 2.5rem;
  }

  .featured-content {
    padding: 40px 20px;
    margin-top: 50px;
  }

  .featured-content::after {
    width: 60px;
    height: 60px;
  }

  .featured-vector {
    display: none;
  }

  .decoration-line {
    width: 200px;
    height: 100px;
  }

  .services-curve {
    height: 80px;
    clip-path: ellipse(100% 100% at 50% 100%);
  }

  .platform-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .clients-logos {
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .client-logo {
    width: 120px;
    height: 60px;
  }

  .projects-layout {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(6, 200px);
  }

  .standards-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .connection-line {
    display: none;
  }

  .standards-title {
    font-size: 2rem;
  }
}
</style>

<template>
  <nav class="app-nav">
    <!-- 主导航栏 -->
    <div class="nav-container">
      <div class="nav-brand">
        <router-link to="/" class="brand-link">
          <img src="/images/logo.png" alt="logo" class="brand-logo" />
          CGC DIGITAL
        </router-link>
      </div>

      <!-- 移动端汉堡按钮 -->
      <button
        class="menu-toggle"
        aria-label="打开菜单"
        aria-expanded="false"
        @click="toggleMenu"
      >
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </button>

      <ul class="nav-menu" :class="{ open: isMenuOpen }">
        <li class="nav-item" @click="closeMenu">
          <router-link to="/" class="nav-link">首页</router-link>
        </li>

        <li class="nav-item" @click="closeMenu">
          <router-link to="/about" class="nav-link">关于我们</router-link>
        </li>

        <li class="nav-item" @click="closeMenu">
          <router-link to="/product-center" class="nav-link"
            >产品中心</router-link
          >
        </li>

        <li class="nav-item" @click="closeMenu">
          <router-link to="/industry-solution" class="nav-link"
            >行业解决方案</router-link
          >
        </li>

        <li class="nav-item" @click="closeMenu">
          <router-link to="/contact" class="nav-link">联系我们</router-link>
        </li>
      </ul>
    </div>

    <!-- 产品中心二级导航栏 -->
    <div v-if="showProductSubNav" class="product-sub-nav">
      <div class="sub-nav-container">
        <ul class="sub-nav-menu">
          <li class="sub-nav-item">
            <router-link
              to="/product-center/asset-management"
              class="sub-nav-link"
              active-class="active"
              >资产管理</router-link
            >
          </li>
          <li class="sub-nav-item">
            <router-link
              to="/product-center/power-distribution"
              class="sub-nav-link"
              active-class="active"
              >配电监控</router-link
            >
          </li>
          <li class="sub-nav-item">
            <router-link
              to="/product-center/energy-management"
              class="sub-nav-link"
              active-class="active"
              >EMS能源管理</router-link
            >
          </li>
        </ul>
      </div>
    </div>

    <!-- 解决方案二级导航栏 -->
    <div v-if="showSolutionSubNav" class="solution-sub-nav">
      <div class="sub-nav-container">
        <ul class="sub-nav-menu">
          <li class="sub-nav-item">
            <router-link
              to="/industry-solution/smart-microgrid"
              class="sub-nav-link"
              active-class="active"
              >智能微电网</router-link
            >
          </li>
          <li class="sub-nav-item">
            <router-link
              to="/industry-solution/power-monitoring"
              class="sub-nav-link"
              active-class="active"
              >电力监控系统</router-link
            >
          </li>
          <li class="sub-nav-item">
            <router-link
              to="/industry-solution/residential-energy"
              class="sub-nav-link"
              active-class="active"
              >住宅能源</router-link
            >
          </li>
          <li class="sub-nav-item">
            <router-link
              to="/industry-solution/data-center"
              class="sub-nav-link"
              active-class="active"
              >数据中心</router-link
            >
          </li>
          <li class="sub-nav-item">
            <router-link
              to="/industry-solution/building-control"
              class="sub-nav-link"
              active-class="active"
              >楼宇控制</router-link
            >
          </li>
        </ul>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

// 判断是否显示产品中心二级导航
const showProductSubNav = computed(() => {
  return route.path.startsWith("/product-center");
});

// 判断是否显示解决方案二级导航
const showSolutionSubNav = computed(() => {
  return route.path.startsWith("/industry-solution");
});

// 移动端菜单开关
const isMenuOpen = ref(false);
const toggleMenu = () => (isMenuOpen.value = !isMenuOpen.value);
const closeMenu = () => (isMenuOpen.value = false);

// 路由变化时关闭移动端菜单
watch(
  () => route.path,
  () => {
    isMenuOpen.value = false;
  }
);
</script>

<style scoped>
.app-nav {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  background: #1e1f25;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.nav-container {
  position: relative;
  max-width: 1920px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 20px;
  height: 80px;
}

.nav-brand {
  position: absolute;
  left: calc((100vw - 1200px) / 2);
  top: 50%;
  transform: translateY(-50%);
}

.nav-brand .brand-link {
  font-family: Poppins;
  font-size: 22px;
  font-weight: 600;
  line-height: normal;
  letter-spacing: 0em;
  font-variation-settings: "opsz" auto;
  color: #ffffff;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-menu {
  display: flex;
  flex-direction: row;
  padding: 0px 24px;
  gap: 60px;
  list-style: none;
  margin: 0;
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
}

.nav-item {
  position: relative;
}

.nav-link {
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  line-height: 22.4px;
  letter-spacing: 0px;
  color: #ffffff;
  text-decoration: none;
  padding: 10px 0;
  display: block;
  transition: color 0.3s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #ca2a22;
}

.dropdown-toggle {
  cursor: pointer;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 10px 0;
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s;
  list-style: none;
  margin: 0;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-link {
  color: #2c3e50;
  text-decoration: none;
  padding: 8px 20px;
  display: block;
  transition: background-color 0.3s;
}

.dropdown-link:hover {
  background-color: #f8f9fa;
  color: #3498db;
}

/* 产品中心和解决方案二级导航样式 */
.product-sub-nav,
.solution-sub-nav {
  background: #1e1f25;
  border-top: 1px solid #2a2b31;
  height: 50px;
  display: flex;
  align-items: center;
}

.sub-nav-container {
  max-width: 1920px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
}

.sub-nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 40px;
  justify-content: flex-end; /* 将导航项移到右边 */
  padding-right: 20px;
}

.sub-nav-item {
  position: relative;
}

.sub-nav-link {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 19.6px;
  letter-spacing: 0px;
  color: #b0b3b8;
  text-decoration: none;
  padding: 15px 0;
  display: block;
  transition: color 0.3s;
  position: relative;
}

.sub-nav-link:hover {
  color: #ffffff;
}

.sub-nav-link:hover::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #ca2a22;
  transform: scaleX(1);
}

.sub-nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #ca2a22;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.sub-nav-link.active {
  color: #ca2a22;
}

.sub-nav-link.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #ca2a22;
  transform: scaleX(1);
}

/* 移动端汉堡按钮基础样式 */
.menu-toggle {
  display: none;
  width: 40px;
  height: 40px;
  background: transparent;
  border: 1px solid #444;
  border-radius: 6px;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
  color: #fff;
}

.menu-toggle .bar {
  display: block;
  width: 20px;
  height: 2px;
  background: #fff;
}

/* Responsive Design */
@media (max-width: 1200px) and (min-width: 769px) {
  .nav-brand {
    left: calc((100vw - 700px) / 2);
  }
}

@media (max-width: 768px) {
  /* 显示汉堡按钮，隐藏横向菜单 */
  .menu-toggle {
    display: inline-flex;
    margin-left: auto;
  }

  .nav-menu {
    position: fixed;
    left: 0;
    right: 0;
    top: 80px; /* 紧贴主导航底部 */
    background: #1e1f25;
    border-top: 1px solid #2a2b31;
    display: none;
    flex-direction: column;
    padding: 12px 20px 16px;
    gap: 8px;
    z-index: 999;
  }

  .nav-menu.open {
    display: flex;
  }

  .nav-link {
    font-size: 16px;
    line-height: 22px;
    padding: 10px 0;
  }

  /* 二级导航移动端滚动与居中 */
  .sub-nav-menu {
    gap: 16px;
    padding: 0 10px 0 10px;
    justify-content: center;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .sub-nav-menu::-webkit-scrollbar {
    height: 0;
  }
}

.sub-nav-link {
  font-size: 12px;
  padding: 12px 0;
}

.brand-logo {
  height: 32px;
  width: auto;
  flex-shrink: 0;
}
</style>

<template>
  <div class="residential-energy">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <div class="container">
          <!-- 左侧图片区域 -->
          <div class="hero-left">
            <div class="mockup-container">
              <img
                src="/images/Mockup.png"
                alt="手机模型"
                class="mockup-image"
              />
              <img src="/images/能源.png" alt="能源图标" class="energy-icon" />

              <!-- 周围的在线图标 -->
              <div class="floating-icons">
                <div class="icon-item icon-1">
                  <div class="icon-circle">
                    <i class="fas fa-plus"></i>
                  </div>
                </div>
                <div class="icon-item icon-2">
                  <div class="icon-circle">
                    <i class="fas fa-video"></i>
                  </div>
                </div>
                <div class="icon-item icon-3">
                  <div class="icon-circle">
                    <i class="fas fa-chart-line"></i>
                  </div>
                </div>
                <div class="icon-item icon-4">
                  <div class="icon-circle">
                    <i class="fas fa-magic"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧内容区域 -->
          <div class="hero-right">
            <div class="content-wrapper">
              <h1 class="hero-title">
                Realtor Home<br />
                Monitoring system
              </h1>
              <p class="hero-subtitle">
                You provide customized intelligent energy solutions that
                accurately match your energy needs. Whether it's energy
                conservation and emission reduction or cost control, we can
                provide professional support to make your energy management more
                scientific and reasonable.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Model Section -->
    <section class="model-section">
      <div class="container">
        <!-- 居中的标题区域 -->
        <div class="solution-header">
          <div class="solution-tag">碳平衡解决方案</div>
          <h2 class="section-title">
            住宅能源管理系统是一个基于云平台的解决方案，专<br />
            门用于监视、预测和优化DER的运行
          </h2>
          <p class="section-subtitle">
            全设备能耗监控+微网自供电优化+清洁能源力图规划+AI能源分少推荐，实<br />
            现"监控-调控-优化"闭环。
          </p>

          <!-- 操作按钮（替换中央控制点，结构对齐 PowerMonitoring） -->
          <div class="action-buttons">
            <button class="btn-smart">
              <span class="btn-icon">●</span>
              全能能耗监视
            </button>
            <button class="btn-smart">
              <span class="btn-icon">●</span>
              碳排放看板
            </button>
          </div>
        </div>

        <!-- 左右布局区域 -->
        <div class="model-content">
          <div class="model-left">
            <!-- 功能列表 -->
            <div class="features-list">
              <div class="feature-item">
                <div class="feature-icon">
                  <i class="fas fa-solar-panel"></i>
                </div>
                <div class="feature-content">
                  <h4>分布式能源</h4>
                  <p class="feature-subtitle">清洁能源（主要是自力发电）</p>
                  <p class="feature-desc">
                    分布式光伏、风电、小水电等清洁能源设备<br />
                    可接入分布式发电设备（主要是自力发电用可再生能源）<br />
                    三级供电组、小型燃气轮机、生物质发电、垃圾发电等
                  </p>
                </div>
              </div>

              <div class="feature-item">
                <div class="feature-icon">
                  <i class="fas fa-battery-full"></i>
                </div>
                <div class="feature-content">
                  <h4>灵活性负荷</h4>
                  <p class="feature-subtitle">电动汽车充电负荷</p>
                  <p class="feature-desc">
                    利用充电及V2G技术<br />
                    核实空调负荷、地源热泵<br />
                    城市供热设备，具备较好的热惰性常规，功率调节可对用户用能体验影响小
                  </p>
                </div>
              </div>

              <div class="feature-item">
                <div class="feature-icon">
                  <i class="fas fa-cogs"></i>
                </div>
                <div class="feature-content">
                  <h4>储能及P2X</h4>
                  <p class="feature-subtitle">电池储能→P2X：Power toX</p>
                  <p class="feature-desc">
                    即电能转换，电解水、电制冷+蓄冷热泵、垃圾发电+蓄热等，具有一定<br />
                    的能量存储能力的系统设备，家庭储能、家庭供热设备等
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="model-right">
            <!-- 图片区域 -->
            <div class="banner-container">
              <!-- 图片上的标注点 -->
              <div class="annotation-points">
                <div class="point point-1">
                  <div class="point-dot"></div>
                  <div class="point-label">
                    <span>分布式发电</span>
                    <small>如光伏、风电</small>
                  </div>
                </div>

                <div class="point point-2">
                  <div class="point-dot"></div>
                  <div class="point-label">
                    <span>控制系统</span>
                    <small>实现能源管理与调度</small>
                  </div>
                </div>

                <div class="point point-3">
                  <div class="point-dot"></div>
                  <div class="point-label">
                    <span>储能系统</span>
                    <small>如电池储能</small>
                  </div>
                </div>

                <div class="point point-4">
                  <div class="point-dot"></div>
                  <div class="point-label">
                    <span>负荷</span>
                    <small>家庭、工业用电设备</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Mobile App Section -->
    <section class="mobile-app">
      <div class="container">
        <div class="app-content">
          <!-- 背景图片 -->
          <div class="app-background">
            <img src="/images/cgcbg.png" alt="背景" class="bg-image" />
          </div>

          <!-- 内容区域 -->
          <div class="app-text-content">
            <h2 class="app-title">Transforming homes into smart powerhouses</h2>

            <div class="app-features">
              <div class="app-feature">
                <div class="feature-icon">
                  <i class="fas fa-solar-panel"></i>
                </div>
                <div class="feature-text">
                  <h4>
                    Unlock and extend whole-home battery backup with fewer
                    batteries
                  </h4>
                </div>
              </div>

              <div class="app-feature">
                <div class="feature-icon">
                  <i class="fas fa-charging-station"></i>
                </div>
                <div class="feature-text">
                  <h4>Avoid demand charges and maximize NEM 3.0 credits</h4>
                </div>
              </div>

              <div class="app-feature">
                <div class="feature-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <div class="feature-text">
                  <h4>
                    Save through flexible, transparent demand response programs
                  </h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Mobile App Display Section -->
    <section class="mobile-app-display">
      <div class="container">
        <div class="title-box">
          <h2 class="mobile-title">家居微电网智能中枢</h2>
          <p class="mobile-subtitle">
            无缝闪间大功耗、储能与家庭负荷，始终清洁能源生态闭环。
          </p>
        </div>
        <div class="mobile-display-content">
          <!-- 左侧手机图片 -->
          <div class="mobile-phones">
            <img
              src="/images/手机组.png"
              alt="手机应用展示"
              class="phones-image"
            />
          </div>

          <!-- 右侧内容 -->

          <div class="mobile-content">
            <!-- 标题盒子 -->

            <div class="mobile-features">
              <div class="mobile-feature">
                <div class="feature-icon-mobile">
                  <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="feature-content">
                  <h4>成本掌控</h4>
                  <p>AI智能优化运营策略，电费支出优化</p>
                </div>
              </div>

              <div class="mobile-feature">
                <div class="feature-icon-mobile">
                  <i class="fas fa-bolt"></i>
                </div>
                <div class="feature-content">
                  <h4>用电无忧</h4>
                  <p>原网断电环境下，重要用电设备不受影响</p>
                </div>
              </div>

              <div class="mobile-feature">
                <div class="feature-icon-mobile">
                  <i class="fas fa-leaf"></i>
                </div>
                <div class="feature-content">
                  <h4>绿色未来</h4>
                  <p>最大化清洁能源占比，让家庭电器可持续</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- SolarSync ESS Section -->
    <section class="solarsync-section">
      <div class="container">
        <div class="solarsync-content">
          <h2 class="section-title">SolarSync ESS Autopilot</h2>
          <p class="section-subtitle">
            AI autonomously slashes energy costs by 40%+ via peak-valley
            arbitrage while ensuring UPS-grade backup for vital loads during
            outages.
          </p>

          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-solar-panel"></i>
              </div>
              <p>在任何状态下，最大限度地节省您的太阳能和储能系统成本。</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-cog"></i>
              </div>
              <p>智能算法精准调控，确保设备始终运行在功率限制范围内。</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-chart-bar"></i>
              </div>
              <p>在任何状态下，最大限度地节省您的太阳能和储能系统成本。</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-lightbulb"></i>
              </div>
              <p>
                系统实时监测电力需求，自动优化负载分配，确保在停电时优先保障关键设备的电力供应。
              </p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-bolt"></i>
              </div>
              <p>系统自动识别峰谷电价，智能安排充电时间，能源利用更高效。</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-database"></i>
              </div>
              <p>
                优先保障重要设备的电力供应，在突发状况下，生活与工作核心设备依然稳定运行。
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 住宅能源解决方案页面逻辑
</script>

<style scoped>
.residential-energy {
  padding-top: 130px;
  min-height: 100vh;
  background: #1e1f25;
  
  color: #ffffff;
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  background-image: url('/images/Ellipse <EMAIL>'),url('/images/Ellipse <EMAIL>'),url('/images/<EMAIL>');
  /* 1st: ellipse TL, 2nd: ellipse BL, 3rd: grid cover */
  background-repeat: no-repeat, no-repeat, no-repeat;
  background-position: left 148px top 24px, left 398px bottom 224px, top center;
  background-size: 320px auto, 320px auto, 100% 100%;
  display: flex;
  align-items: center;
}

.hero-bg-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e1f25 0%, #2a2b31 50%, #1e1f25 100%);
  position: relative;
}

.hero-bg-placeholder::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 40%,
      rgba(52, 152, 219, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 60%,
      rgba(231, 76, 60, 0.1) 0%,
      transparent 50%
    );
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 80px;
  
}

/* Hero Left - 图片区域 */
.hero-left {
  flex: 1;
  position: relative;
}

.mockup-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mockup-image {
  max-width: 300%;
  height: auto;
  z-index: 2;
}

.energy-icon {
  position: absolute;
  top: 40px;
  right: 70px;
  width: 150px;
  height: 160px;
  z-index: -1;
}

.floating-icons {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.icon-item {
  position: absolute;
}

.icon-item.icon-1 {
  top: 1%;
  left: 5%;
}

.icon-item.icon-2 {
  top: 50%;
  right: -5%;
}

.icon-item.icon-3 {
  bottom: 76%;
  left: -15%;
}

.icon-item.icon-4 {
  bottom: 60%;
  right: 90%;
}

.icon-circle {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
  animation: float 3s ease-in-out infinite;
}

.icon-item:nth-child(2) .icon-circle {
  animation-delay: 0.5s;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
}

.icon-item:nth-child(3) .icon-circle {
  animation-delay: 1s;
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  box-shadow: 0 10px 30px rgba(243, 156, 18, 0.3);
}

.icon-item:nth-child(4) .icon-circle {
  animation-delay: 1.5s;
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  box-shadow: 0 10px 30px rgba(155, 89, 182, 0.3);
}

/* Hero Right - 内容区域 */
.hero-right {
  flex: 1;
  position: relative;
  background-image: url("/images/oval.png");
  background-repeat: no-repeat;
  background-position: left -150px;
  background-size: 800px;
}

.content-wrapper {
  position: relative;
  padding: 40px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #ffffff 0%, #b0b3b8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 16px;
  line-height: 1.8;
  color: #b0b3b8;
  margin-bottom: 40px;
}

.hero-buttons {
  display: flex;
  gap: 20px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: #ffffff;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #ffffff;
  border: 2px solid #3498db;
  padding: 13px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-secondary:hover {
  background: #3498db;
  color: #ffffff;
  transform: translateY(-2px);
}

.hero-image {
  flex: 0 0 500px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Phone Mockup */
.phone-mockup {
  position: relative;
  width: 280px;
  height: 560px;
  background: #000000;
  border-radius: 30px;
  padding: 8px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 22px;
  overflow: hidden;
  position: relative;
}

.phone-interface {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.interface-header {
  margin-bottom: 30px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
}

.signal-icons {
  display: flex;
  gap: 4px;
}

.signal-dot {
  width: 8px;
  height: 8px;
  background: #27ae60;
  border-radius: 50%;
}

.interface-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.home-control-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.control-card {
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.3);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s;
}

.control-card:hover {
  background: rgba(52, 152, 219, 0.2);
  transform: translateY(-2px);
}

.card-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.card-label {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
}

.energy-chart {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
}

.chart-title {
  color: #b0b3b8;
  font-size: 14px;
  margin-bottom: 15px;
}

.chart-bars {
  display: flex;
  gap: 8px;
  align-items: end;
  height: 60px;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(180deg, #3498db 0%, #2980b9 100%);
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  animation: chartGrow 2s ease-out;
}

@keyframes chartGrow {
  from {
    height: 0;
  }
  to {
    height: var(--height);
  }
}

/* Floating Elements */
.floating-element {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(52, 152, 219, 0.1);
  border: 2px solid #3498db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: float 3s ease-in-out infinite;
}

.element-1 {
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.element-2 {
  top: 60%;
  right: -15%;
  animation-delay: 1s;
}

.element-3 {
  bottom: 20%;
  left: -5%;
  animation-delay: 2s;
}

.element-icon {
  font-size: 24px;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Annotation Styles */
.annotation {
  position: absolute;
  z-index: 10;
}

.annotation-1 {
  top: 15%;
  left: 15%;
}

.annotation-2 {
  top: 15%;
  right: 15%;
}

.annotation-3 {
  bottom: 25%;
  left: 15%;
}

.annotation-4 {
  bottom: 25%;
  right: 15%;
}

.annotation-dot {
  width: 12px;
  height: 12px;
  background: #e74c3c;
  border-radius: 50%;
  border: 2px solid #ffffff;
  cursor: pointer;
  animation: pulse 2s infinite;
}

.annotation-label {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.annotation:hover .annotation-label {
  opacity: 1;
}

.annotation-detail {
  font-size: 10px;
  color: #b0b3b8;
  margin-top: 4px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* Features Section - 删除重复样式，已在下方定义 */

/* Feature Visual */
.feature-visual {
  position: relative;
  width: 400px;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-circle {
  position: relative;
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 20px 60px rgba(52, 152, 219, 0.3);
}

.center-logo {
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
}

.orbit-item {
  position: absolute;
  width: 80px;
  height: 80px;
  background: rgba(52, 152, 219, 0.1);
  border: 2px solid #3498db;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: orbit 10s linear infinite;
}

.orbit-1 {
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 0s;
}

.orbit-2 {
  top: 50%;
  right: -40px;
  transform: translateY(-50%);
  animation-delay: -3.33s;
}

.orbit-3 {
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: -6.66s;
}

.orbit-icon {
  font-size: 24px;
}

@keyframes orbit {
  0% {
    transform: rotate(0deg) translateX(160px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(160px) rotate(-360deg);
  }
}

/* Mobile App Section */
.mobile-app {
  background: #1e1f25;

  color: #ffffff;
  position: relative;
  overflow: hidden;
}

.app-content {
  position: relative;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: flex-end; /* push content to right */
}

.app-background {
  position: absolute;
  top: -6%;
  left: -64%;
  right: 0;
  bottom: -6%;
  z-index: 1;
}

.bg-image {
  width: 95%;
  height: 95%;
  object-fit: contain;
}

.app-text-content {
  position: relative;
  z-index: 2;
  padding: 80px 0;
  display: flex;
  flex-direction: column;
  max-width: 63%;
  margin-left: auto;
  margin-right: 0;
  transform: translateX(292px); /* nudge to match comp */
}

.app-title {
  font-size: 36px;
  font-weight: 700;
  line-height: 1.25;
  margin-bottom: 16px;
  color: #f2f2f2;
}

.app-features {
  display: flex;
  flex-direction: column;
  gap: 22px;
}

.app-feature {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
}

.app-feature .feature-icon {
  width: 56px;
  height: 56px;
  background: #d9d9d9; /* grey for 1st and 3rd */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0392b; /* red icon on grey */
  font-size: 22px;
  flex-shrink: 0;
}

/* Middle feature uses red badge with white icon */
.app-features .app-feature:nth-child(2) .feature-icon {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: #ffffff;
}

.app-feature .feature-text {
  max-width: 82%;
}

.app-feature .feature-text h4 {
  font-size: 16px;
  color: #c8c8c8;
  font-weight: 500;
  margin: 0;
  line-height: 1.6;
}

/* Mobile App Display Section */
.mobile-app-display {
  background: #f8f9fa;
  background-image: url('/images/有一家设计素材铺淘宝店<EMAIL>');

  /* padding: 120px 0; */
  color: #333;
  background-repeat: no-repeat;
  background-position: right -290px center; /* shift image further right */
  background-size: auto;
}

.mobile-display-content {
  display: flex;
  align-items: center;
  gap: 200px;
  max-width: 1200px;
  margin: 0 auto;
}

.mobile-phones {
  flex: 1;
  text-align: center;
}

.phones-image {
  max-width: 250%;
  margin-left: -390px;
  height: auto;
  max-height: 600px;
}

.mobile-content {
  flex: 1;
  padding-left: 80px;
}

.mobile-title {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.2;
}

.mobile-subtitle {
  font-size: 18px;
  color: #7f8c8d;
  margin-bottom: 50px;
  line-height: 1.6;
}

.title-box {
  margin: 0 auto;
  margin-top: 100px;
  max-width: 600px;
  text-align: center;
}

.title-box .mobile-subtitle {
  margin-bottom: 0;
}

.mobile-features {
  display: flex;
  flex-direction: column;
  gap: 60px;
}

.mobile-feature {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.feature-icon-mobile {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #27ae60;
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 5px;
}

.feature-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.feature-content p {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.5;
}

/* SolarSync ESS Section */
.solarsync-section {
  /* Use public root path and do NOT override with background shorthand */
  background-image: url('/images/路径 <EMAIL>');
  background-color: #1e1f25;
  padding: 120px 0;
  color: #ffffff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 88%;
}

.solarsync-content {
  text-align: center;
}

.solarsync-section .section-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 30px;
  color: #ffffff;
}

.solarsync-section .section-subtitle {
  font-size: 18px;
  color: #b0b3b8;
  margin-bottom: 80px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: rgba(47, 50, 65, 0.5);
  padding: 25px 40px;
  border-radius: 20px;
  text-align: center;
  backdrop-filter: blur(13px);
  border: 1px solid;
  border-image: linear-gradient(113deg, rgba(37, 41, 44, 0) 40%, #2b2c31 99%) 1;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-card .feature-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  margin: 0 auto 20px;
}

.feature-card p {
  font-size: 14px;
  color: #b0b3b8;
  line-height: 1.6;
  margin: 0;
}

/* Model Section - 保留主要样式，删除重复定义 */
.model-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.model-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0e0e0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.model-section .container {
  position: relative;
  z-index: 2;
}

.solution-header {
  text-align: center;

  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.solution-tag {
  display: inline-block;
  background: #505360;
  color: #ffffff;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
}

.model-section .section-title {
  font-size: 36px;
  font-weight: 700;
  color: #12141d;
  line-height: 1.4;
  margin-bottom: 20px;
  text-align: center;
}

.model-section .section-subtitle {
  font-size: 16px;
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 40px;
  text-align: center;
}

.central-controls {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-bottom: 60px;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.control-dot {
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  position: relative;
}

.control-dot::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid #667eea;
  border-radius: 50%;
  animation: ripple 2s infinite;
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.8);
    opacity: 0;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 10px;
}

.btn-smart {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  border-radius: 25px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #111827;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.btn-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.model-content {
  display: flex;
  gap: 64px; /* 两列间距略收紧，更接近设计 */
  align-items: flex-start;
  max-width: 1200px;
  margin: -80px auto 0; /* 向上移动40px */
  padding: 0 20px;
  position: relative; /* 确保子元素相对定位正确 */
}

.model-left {
  flex: 1;
  max-width: 560px; /* 收窄左列宽度，接近设计 */
}

.model-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  margin-right: -140px; /* 图片区域向右移动80px */
}

/* features-list 样式重复，已合并到下方 */

/* banner-container 样式重复，已合并到下方 */

/* point 相关样式重复，已合并到下方 */

/* 响应式样式重复，已合并 */

/* Model Section Styles - 合并重复样式 */

.features-list {
  display: flex;
  flex-direction: column;
  gap: 48px; /* 严格匹配设计图：增大项间距 */
  width: 200%;
  max-width: none;
}

/* 响应式设计 - 删除重复样式 */

.feature-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  text-align: left;
  padding: 8px 0; /* 列表项自身上下内边距较小 */
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  gap: 16px;
  border-bottom: none; /* 设计中无分隔线，靠网格背景表现层次 */
}
.feature-item:first-child { padding-top: 0; }
.feature-item:last-child { padding-bottom: 0; }
.feature-icon {
  width: 68px; /* 严格匹配设计图：增大图标尺寸 */
  height: 68px;
  background: #DDDDDD; /* 严格匹配设计图：更浅、更平的背景 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626; /* 严格匹配设计图：更纯正的红色 */
  font-size: 28px; /* 严格匹配设计图：增大图标字号 */
  flex-shrink: 0;
  transition: all 0.3s ease;
  /* 移除阴影和位移，追求扁平化 */
}

.feature-content {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.feature-content h4 {
  font-size: 24px; /* 严格匹配设计图：增大标题字号 */
  font-weight: 700;
  color: #111827; /* 严格匹配设计图：近黑色 */
  margin-bottom: 8px; /* 增大标题下方间距 */
  line-height: 1.3;
  text-align: left;
}

.feature-subtitle {
  /* 严格匹配设计图：与描述样式统一，视觉合并 */
  font-size: 14px;
  color: #6b7280;
  font-weight: 400;
  line-height: 1.8;
  margin-bottom: 0; /* 移除与下方描述的间距 */
  text-align: left;
  letter-spacing: normal;
}

.feature-desc {
  font-size: 14px;
  color: #6b7280; /* 严格匹配设计图：统一文字颜色 */
  line-height: 1.8; /* 严格匹配设计图：调整行高 */
  letter-spacing: normal; /* 严格匹配设计图：移除字距 */
  font-weight: 400;
  margin: 0;
  text-align: left;
  max-width: 520px;
}

.banner-container {
  position: relative;
  width: 900px; /* 扩大容器，便于完整展示 */
  height: 520px;
  background-image: url("/images/banner1.png");
  background-size: contain; /* 完整显示整张图，不裁切 */
  background-position: right center; /* 贴近右侧居中 */
  background-repeat: no-repeat;
  overflow: visible; /* 确保标注点可以显示在容器外 */
}



.point {
  position: absolute;
  z-index: 3;
}

.point-1 {
  top: 12%;
  right: 18%;
}

.point-2 {
  top: 45%;
  right: 10%;
}

.point-3 {
  bottom: 30%;
  left: 42%;
}

.point-4 {
  bottom: 18%;
  right: 25%;
}

.point-dot {
  width: 14px;
  height: 14px;
  background: #ff4757;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
  transition: all 0.3s ease;
}

.point-dot:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.6);
}

.point-dot::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 28px;
  height: 28px;
  border: 2px solid rgba(255, 71, 87, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* pulse 动画重复，已删除 */

.point-label {
  background: #DDDDDD ;
  backdrop-filter: blur(10px);
  padding: 10px 14px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  font-size: 13px;
  white-space: nowrap;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  min-width: 140px;
}

.point-label:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
}

.point-label span {
  display: block;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
  font-size: 14px;
}

.point-label small {
  color: #718096;
  font-size: 11px;
  line-height: 1.4;
}

/* 响应式样式重复，已合并到下方 */

/* 主要响应式样式 - 768px 及以下 */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 40px;
  }

  .hero-title {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 14px;
  }

  .section-title {
    font-size: 28px;
  }

  .app-title {
    font-size: 32px;
  }

  .app-text-content {
    max-width: 100%;
    text-align: center;
    align-items: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .floating-icons,
  .energy-icon,
  .decorative-ellipse,
  .annotation-points {
    display: none;
  }

  .mobile-display-content {
    flex-direction: column;
    gap: 50px;
    text-align: center;
  }

  .mobile-content {
    padding-left: 0;
  }

  .mobile-title {
    font-size: 32px;
  }

  .mobile-subtitle {
    font-size: 16px;
  }

  .title-box {
    padding: 30px 20px;
    margin: 0 auto 40px auto;
    max-width: 90%;
  }

  .phones-image {
    max-height: 400px;
  }

  .model-content {
    flex-direction: column;
    gap: 40px;
  }

  .solution-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .model-section .section-title {
    font-size: 28px;
  }

  .central-controls {
    flex-direction: column;
    gap: 20px;
  }

  .features-list {
    gap: 30px;
  }

  .feature-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 20px;
    padding: 25px 20px;
  }

  .feature-icon {
    width: 70px;
    height: 70px;
    font-size: 20px;
  }

  .feature-content {
    align-items: center;
  }

  .feature-content h4 {
    font-size: 20px;
    text-align: center;
  }

  .feature-subtitle,
  .feature-desc {
    font-size: 14px;
    text-align: center;
  }

  .banner-container {
    width: 100%;
    height: 300px;
  }

  .point-label {
    font-size: 12px;
    padding: 8px 12px;
  }

  .point-dot {
    width: 12px;
    height: 12px;
    box-shadow: 0 0 0 4px rgba(231, 76, 60, 0.2);
  }

  .solution-tag {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 28px;
  }

  .section-title {
    font-size: 24px;
  }

  .app-title {
    font-size: 28px;
  }

  .mockup-image {
    max-width: 300px;
  }

  .mobile-title {
    font-size: 28px;
  }

  .phones-image {
    max-height: 300px;
  }

  .title-box {
    padding: 25px 15px;
    margin: 0 auto 30px auto;
    max-width: 95%;
  }
}
</style>

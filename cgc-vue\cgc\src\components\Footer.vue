<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <!-- Company Info -->
        <div class="footer-section company-info">
          <div class="footer-logo">
            <img src="/images/logo.png" alt="CGC Digital Logo" />
            <span>CGC DIGITAL<span class="red-dot">.</span></span>
          </div>
          <p class="company-description">
            CGC DIGITAL PROVIDES A VARIETY OF SERVICES COVERING THE LIFECYCLE OF
            YOUR PROJECT
          </p>
          <div class="social-links">
            <a href="#" class="social-link"
              ><i class="fab fa-facebook-f"></i
            ></a>
            <a href="#" class="social-link"><i class="fab fa-weibo"></i></a>
            <a href="#" class="social-link"><i class="fab fa-weixin"></i></a>
          </div>
          <div class="newsletter">
            <h4>Subscribe to our newsletter</h4>
            <div class="newsletter-form">
              <input type="email" placeholder="Enter your email address" />
              <button type="submit">Send</button>
            </div>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="footer-section quick-links">
          <h3>Quick Links</h3>
          <ul class="footer-links">
            <li><a href="#">SEACE</a></li>
            <li><a href="#">Company</a></li>
            <li><a href="#">SEACE DIGITAL</a></li>
            <li><a href="#">Company</a></li>
            <li><a href="#">Company</a></li>
          </ul>
        </div>

        <!-- Contacts -->
        <div class="footer-section contacts">
          <h3>Contacts</h3>
          <div class="contact-item">
            <div class="contact-icon email">
              <i class="fas fa-envelope"></i>
            </div>
            <span>*******@outlook.com</span>
          </div>
          <p>Technical Support</p>
          <div class="contact-item">
            <div class="contact-icon phone">
              <i class="fas fa-phone"></i>
            </div>
            <div>
              <div>0086+186****7876</div>
            </div>
          </div>
          <p>Sales and Business Inquiries</p>
          <div class="contact-item">
            <div class="contact-icon phone">
              <i class="fas fa-phone"></i>
            </div>
            <div>
              <div>0086+186****7876</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Bottom -->
      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <p>&copy; CGC DIGITAL &copy; All rights reserved.</p>
          <div class="footer-bottom-links">
            <a href="#">Terms of Service</a>
            <a href="#">Privacy Policy</a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// Footer component logic
</script>

<style scoped>
.footer {
  background: #2a2d35;
  color: #ffffff;
  padding: 60px 0 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2.5fr 1.5fr 2fr;
  gap: 40px 120px;
  padding-bottom: 40px;
  border-bottom: 1px solid #444;
}

.footer-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #ffffff;
}

/* Company Info */
.company-info {
  max-width: 350px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.footer-logo span {
  font-size: 1.5rem;
  font-weight: bold;
  margin-left: 10px;
}

.footer-logo .red-dot {
  color: #ca2a22;
}

.company-description {
  margin: 20px 0;
  line-height: 1.6;
  color: #ffffff;
  font-size: 0.9rem;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
}

.social-link {
  color: #fff;
  margin-right: 20px;
  font-size: 1.5rem;
  text-decoration: none;
}

.social-link:hover {
  color: #ca2a22;
}

.newsletter h4 {
  margin-bottom: 15px;
  font-weight: 500;
  color: #a5adcf;
}

.newsletter-form {
  display: flex;
  background: rgba(47, 50, 65, 0.5);
  border: 1px solid #34384c;
  border-radius: 30px;
  padding: 5px;
  overflow: hidden;
}

.newsletter-form input {
  flex-grow: 1;
  padding: 10px 15px;
  border: none;
  background: transparent;
  color: #fff;
  outline: none;
}

.newsletter-form input::placeholder {
  color: #888;
}

.newsletter-form button {
  padding: 10px 25px;
  border: none;
  background: linear-gradient(253deg, #ca2a22 1%, #a5160f 97%);
  color: #fff;
  cursor: pointer;
  border-radius: 30px;
  font-weight: bold;
}

/* Footer Links */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px 20px;
}

.footer-links li {
  margin-bottom: 0;
}

.footer-links a {
  color: #b0b0b0;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #00d4aa;
}

/* Contacts */
.contacts {
  margin-left: 150px;
}
.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 10px;
}

.contact-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  background-color: #ca2a22;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-item span,
.contact-item div {
  color: #fff;
  font-size: 0.9rem;
  line-height: 1.4;
}
.contacts p {
  color: #a5adcf;
  margin-bottom: 10px;
}
/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid #4a4d55;
  padding: 20px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  color: #888;
  font-size: 0.9rem;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 30px;
}

.footer-bottom-links a {
  color: #888;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #00d4aa;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .newsletter-form {
    flex-direction: column;
  }
}
</style>

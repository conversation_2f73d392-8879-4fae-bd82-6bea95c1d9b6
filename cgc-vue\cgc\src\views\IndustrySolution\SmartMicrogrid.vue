<template>
  <div class="power-monitoring-page">
    <!-- Header -->

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          智能微电网解决方案<br/>
         <span> 融合三维孪生与AI算法，实现光伏电站从组件级监控到微网协同调度的全链路智能管控，驱动能源自给与碳平衡闭环</span>

        </h1>
      </div>
    </section>

    <!-- 3D Platform Section - Redesigned -->
    <section class="platform-section-redesigned">
      <div class="power-monitoring-container">
        <!-- Background Image -->
        <div class="background-image">
          <img src="/public/images/<EMAIL>" alt="Power Monitoring System" />
        </div>

        <!-- Coded Elements Overlay -->
        <div class="overlay-elements">
          <!-- Top Section - Main Title and Buttons -->
          <div class="top-section">
            <div class="solution-badge">碳平衡解决方案</div>
            <h1 class="main-title">四维一体管控引擎</h1>
            <p class="subtitle">
              全设备能耗追踪+微网自供优化+流热力图预警+AI策略沙盘推演，实操“监控-调度-验证”闭环。
            </p>
            <div class="action-buttons">
              <button class="btn-smart">
                <span class="btn-icon">●</span>
                全域能耗透视
              </button>
              <button class="btn-smart">
                <span class="btn-icon">●</span>
                碳排决策看板
              </button>
            </div>
          </div>

          <!-- Top Left Card - 连接收集 -->
          <div class="card top-left-card">
            <div class="card-header">
              <div class="card-icon red-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <rect x="2" y="3" width="12" height="8" rx="1" fill="currentColor"/>
                  <rect x="4" y="5" width="8" height="1" fill="white"/>
                  <rect x="4" y="7" width="6" height="1" fill="white"/>
                  <rect x="4" y="9" width="4" height="1" fill="white"/>
                </svg>
              </div>
              <h3>碳平衡驾驶舱</h3>
            </div>
            <p class="card-subtitle">动态计算碳减排量，自动匹配碳抵消项目</p>
          </div>

          <!-- Bottom Left Card - 监测分析 -->
          <div class="card bottom-left-card">
            <div class="card-header">
              <div class="card-icon red-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M2 12L6 8L9 11L14 4" stroke="currentColor" stroke-width="2" fill="none"/>
                  <circle cx="6" cy="8" r="1.5" fill="currentColor"/>
                  <circle cx="9" cy="11" r="1.5" fill="currentColor"/>
                  <circle cx="14" cy="4" r="1.5" fill="currentColor"/>
                </svg>
              </div>
              <h3>故障透视</h3>
            </div>
            <div class="card-items">
              <div class="card-item">热点、组件故障三维定位</div>
            </div>
          </div>

          <!-- Top Right Card - 治理优化 -->
          <div class="card top-right-card">
            <div class="card-header">
              <div class="card-icon green-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M13 4L6 11L3 8" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <h3>能源自供中枢</h3>
            </div>
            <div class="card-items">
              <div class="card-item">调度光伏/储能/负载，提高自给率</div>
            </div>
          </div>
          <img src="/public/images/lefttop.png" alt="" class="left-top-image" />
          <img src="/public/images/leftbutton.png" alt="" class="left-button-image">
          <img src="/public/images/leftxian.png" alt="" class="left-xian-image">
        </div>
      </div>
    </section>

    <!-- Text and Features Container -->
    <section class="text-features-container">
      <!-- Text Section -->
      <div class="text-section">
        <div class="text-content">
          <div class="text-badge"><span class="text-badge-content">智能微电网面临的挑战</span></div>
          <p class="main-text">无线组网避免泄露，秒级定位故障，GIS精控提效能，AI报告驱动决策，四维一体降本增效。</p>
        </div>
      </div>

      <!-- Features Section -->
      <div class="features-section">
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon01.svg" alt=""></div>
            <h3>无线部署成本高，公网数据易泄露</h3>
          </div>
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon02.svg" alt=""></div>
            <h3>故障定位模糊，根因诊断效率低</h3>
          </div>
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon03.svg" alt=""></div>
            <h3>运维盲区多，设备利用率不足</h3>
          </div>
          <div class="feature-card">
            <div class="feature-icon red"><img src="/public/images/icon04.svg" alt=""></div>
            <h3>电站运营分析依赖人工，决策滞后</h3>
          </div>
        </div>
      </div>
    </section>

    <!-- Monitoring Section - Redesigned -->
    <section class="monitoring-section-redesigned">
      <div class="curved-container">
        <!-- Background curves -->
     
        
        <div class="monitoring-content-new">
          <div class="section-header">
            <h2 class="monitoring-title-new">
              三维可视化与智能算法深度融合，驱动能源 <br />
              高效协同与减碳目标达成，释放双重价值
            </h2>
            <p class="monitoring-subtitle-new">孪生 AI 智控光伏，全链闭环碳能双优</p>
          </div>


          <!-- 电脑图片展示区域 左侧导航 + 右侧电脑 -->
          <div class="monitoring-layout">
            <!-- 左侧垂直功能列表 -->
            <div class="vertical-tabs">
              <button class="vertical-tab active">配电监控</button>
              <button class="vertical-tab">光伏管理</button>
              <button class="vertical-tab">能碳分析</button>
              <button class="vertical-tab">AI用能分析</button>
              <button class="vertical-tab">三维数字场景模拟</button>
            </div>

            <div class="computer-display-container">
              <div class="computer-image-wrapper">
                <!-- 电脑框架 -->
                <img src="/public/images/容器 84@2x (2)@1x.png" alt="电脑框架" class="computer-frame" />
                <!-- 内嵌内容 -->

              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Application Scenarios -->
    <section class="scenarios-section">
      <h2 class="scenarios-title">智能楼宇控制应用场景</h2>
      <p class="scenarios-subtitle">三维透视电网资产，AI秒级定位故障，守护城市能源命脉</p>

      <div class="scenarios-grid">
        <!-- 工业厂房屋顶 -->
        <div class="scenario-card">
          <img class="scenario-image" src="/public/images/Image-2.png" alt="工业厂房屋顶" />
          <div class="scenario-content">
            <h3>工业厂房屋顶</h3>
            <p>光伏直驱生产线，谷电储能低碳效益生产</p>
          </div>
        </div>

        <!-- 商业综合体 -->
        <div class="scenario-card">
          <img class="scenario-image" src="/public/images/Image-3.png" alt="商业综合体" />
          <div class="scenario-content">
            <h3>商业综合体</h3>
            <p>空调/照明负载动态匹配光伏出力曲线</p>
          </div>
        </div>

        <!-- 离岛社区 -->
        <div class="scenario-card">
          <img class="scenario-image" src="/public/images/Image-4.png" alt="离岛社区" />
          <div class="scenario-content">
            <h3>离岛社区</h3>
            <p>光储微网替代柴油机，静音零排放</p>
          </div>
        </div>

        <!-- 充电站集群 -->
        <div class="scenario-card">
          <img class="scenario-image" src="/public/images/Image-5.png" alt="充电站集群" />
          <div class="scenario-content">
            <h3>充电站集群</h3>
            <p>光伏优先充电车，余电售电网获利</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    
  </div>
</template>

<script setup>
import { ref } from 'vue'

const activeTab = ref(0)
const activeDot = ref(0)
const activeNewTab = ref(0)
const activeNewDot = ref(0)

const tabs = [
  '实时监控',
  '三维建模管理', 
  '数字孪生建设',
  '电能质量分析',
  '能效效率分析'
]

const newTabs = [
  '实时监控',
  '三维建模管理',
  '资产健康管理',
  '电能质量分析',
  '能效效率分析'
]
</script>

<style scoped>
.power-monitoring-page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
}

/* Header */
.header {
  background: #2c3e50;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  height: 60px;
}

.logo img {
  height: 30px;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #ecf0f1;
  text-decoration: none;
  font-size: 14px;
  padding: 0.5rem 0;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.nav-link:hover,
.nav-link.active {
  color: #e74c3c;
  border-bottom-color: #e74c3c;
}

.header-right {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.header-right a {
  color: #ecf0f1;
  text-decoration: none;
  font-size: 12px;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.header-right a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Hero Section */
.hero-section {
  height: 100vh;
  background-image: url('/public/images/Grid <EMAIL>'), url('/public/images/image@1x (13)@1x.png');
  /* 叠加背景图 */

  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  padding-left: 8%;
  position: relative;
  margin-top: 60px;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
}

.hero-badge {
  background: rgba(255, 255, 255, 0.9);
  color: #2c3e50;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  margin-bottom: 2rem;
  display: inline-block;
}

.hero-title {
  font-size: 1.8rem;
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 3rem;
  line-height: 1.6;
}

.hero-button {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.hero-button:hover {
  background-color: #c0392b;
}

/* Features Section (cards with icon and title) */
.features-section {
  padding: 4rem 8% 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.06);
  padding: 1.25rem 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.feature-icon.red {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-icon.red img {
  width: 28px;
  height: 28px;
  transition: filter 0.3s ease;
}

.feature-card h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.5;
}

.feature-card:hover {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  transform: translateY(-4px);
  box-shadow: 0 14px 36px rgba(0,0,0,0.12);
}

.feature-card:hover h3 {
  color: #ffffff;
}

.feature-card:hover .feature-icon.red {
  background: rgba(255, 255, 255, 0.15);
}

.feature-card:hover .feature-icon.red img {
  filter: brightness(0) invert(1);
}

/* Platform Section Redesigned */
.platform-section-redesigned {
  padding: 0;
  margin: 0;
}

.power-monitoring-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #2a2a2a;
}

.background-image {
  background-color: #000100;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-image img {
  width: 50%;
  height: 60%;
  object-fit: cover;
  position: absolute;
  left: 25%;
  top: 40%;
}

.overlay-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

/* Top Section */
.top-section {
  position: absolute;
  top: 8%;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  max-width: 800px;
  width: 90%;
}

.solution-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

.main-title {
  font-size: 2.2rem;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 16px;
  color: white;
}

.subtitle {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 30px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.btn-smart,
.btn-precise {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 25px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-smart {
  background: white;
  color: #333;
}

.btn-precise {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* Cards */
.card {
  position: absolute;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.card-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.red-icon {
  background: #e74c3c;
}

.green-icon {
  background: #27ae60;
}

.card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.card-subtitle {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.card-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-item {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}



/* Card Positions */
.top-left-card {
  top: 32%;
  left: 12%;
}

.bottom-left-card {
  bottom: 5%;
  left: 8%;
}

.top-right-card {
  top: 28%;
  right: 8%;
}

/* Text and Features Container */
.text-features-container {
  background-image: url("/public/images/有一家设计素材铺淘宝店 <EMAIL>");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 6rem 0 1.25rem; /* 减小底部间距，贴合下一段曲线背景 */
}

/* Text Section */
.text-section {
  padding-top: 2rem;
  text-align: center;
}

.text-badge {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.text-badge-content {
  background: rgba(255, 255, 255, 0.8);
  color: #6b7280;
  font-size: 14px;
  padding: 10px 22px;
  border-radius: 999px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.45);
  font-weight: 500;
}

.main-text {
  font-size: 2rem;
  color: #333;
  line-height: 1.75;
  font-weight: 500;
  max-width: 960px;
  margin: 0 auto;
}

/* Features Section */
.features-section {
  padding: 4rem 8% 2rem 8%;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: #ffffff;
  padding: 1.75rem 1.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  min-height: 110px;
}

.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #ffffff;
  margin: 0 auto 1.5rem;
}



.feature-card h3 {
  font-size: 14px;
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 400;
}

/* Monitoring Section Redesigned */
.monitoring-section-redesigned {
  position: relative;
  /* 改为用伪元素绘制背景，以便独立位移背景而不影响容器布局 */
  background: none;
  min-height: 170vh;
  height: auto;
  padding: 80px 0 0 0; /* 保证标题安全距 */
  margin-top: 0; /* 容器不再上移 */
  margin-bottom: -150px;
  z-index: 2;
  overflow: hidden; /* 防止内容越出背景边缘 */
  --bg-shift: 110px; /* 背景向上位移量，可调 */
}

.monitoring-section-redesigned::before {
  content: "";
  position: absolute;
  left: 0;
  top: calc(-1.2 * var(--bg-shift)); /* 背景单独上移 */
  width: 100%;
  height: calc(85% + var(--bg-shift)); /* 增高以覆盖上移后的空白 */
  /* 先铺一层深色渐变兜底，再放置曲线 PNG */
  background:
     url("/public/images/BG@1x (1).png");
  background-size: 100% 100%, 100% 100%;
  background-position: center center, center center;
  background-repeat: no-repeat, no-repeat;
  z-index: 0;
}w

/* 确保内容层级在背景之上 */
.monitoring-section-redesigned > * {
  position: relative;
  z-index: 1;
}

/* 左下角三色条贴图（使用提供的合成图片），贴在背景上且不覆盖内容 */
/* 已回退：移除左下合成彩条贴图 */

/* 响应式微调彩条尺寸与位置 */
@media (max-width: 1200px) {
  .monitoring-section-redesigned {
    --stripe-width: 52%;
    --stripe-height: 14px;
    --stripe-bottom: 24px;
  }
}
@media (max-width: 768px) {
  .monitoring-section-redesigned {
    --stripe-width: 65%;
    --stripe-height: 12px;
    --stripe-bottom: 18px;
  }
}

/* 响应式微调背景上移量，避免小屏过度位移 */
@media (max-width: 1200px) {
  .monitoring-section-redesigned { --bg-shift: 90px; }
}
@media (max-width: 768px) {
  .monitoring-section-redesigned { --bg-shift: 70px; padding-top: 56px; }
}

.curved-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: inherit;
}

.curve-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: #ecf0f1;
  clip-path: ellipse(100% 100% at 50% 0%);
  z-index: 1;
}

.curve-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: #ecf0f1;
  clip-path: ellipse(100% 100% at 50% 100%);
  z-index: 1;
}

.monitoring-content-new {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  min-height: 140vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-bottom: 4rem;
  color: #ffffff;
}

.section-header {
  text-align: center;
  margin-bottom: 1.5rem;
  padding-top: 1rem;
}

.monitoring-title-new {
  font-size: 3rem;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 0.8rem;
  color: white;
  letter-spacing: 1px;
}

.monitoring-subtitle-new {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0;
  font-weight: 300;
}

.monitoring-tabs-new {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0 2.5rem 0;
  flex-wrap: wrap;
  position: relative;
}

.monitoring-tabs-new::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.tab-btn-new {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 12px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  border-bottom: 2px solid transparent;
}

.tab-btn-new.active {
  color: #e74c3c;
  border-bottom-color: #e74c3c;
}

.tab-btn-new:hover {
  color: #e74c3c;
}

.monitoring-display-new {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: flex-start;
  max-width: 1200px;
  margin: 1.5rem auto 0;
  padding: 0 2rem;
}

.monitoring-info-new h3 {
  font-size: 1.6rem;
  margin-bottom: 1.5rem;
  color: white;
  font-weight: 600;
}

.monitoring-info-new p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.8;
  margin-bottom: 0;
  font-size: 14px;
}

.monitoring-stats-new {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  margin-top: 0;
  text-align: right;
  align-items: flex-end;
}

.stat-item-new {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.1rem 0;
  justify-content: flex-end;
}

.stat-number-new {
  font-size: 2.2rem;
  font-weight: bold;
  color: #e74c3c;
  min-width: 60px;
}

.stat-label-new {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.2;
}

/* 电脑图片展示区域样式 */
.computer-display-container {
  width: 100%;
  max-width: 1200px;
  margin: 0;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  background: rgba(245, 244, 244, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(155, 151, 151, 0.25) inset, 0 10px 30px rgba(0, 0, 0, 0.2);
}

.computer-image-wrapper {
  position: relative;
  width: 100%;
  max-width: 800px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.computer-frame {
  width: 110%;
  height: auto;
  max-width: 840px;
  object-fit: contain;
  position: relative;
  z-index: 2;
}

.computer-screen {
  position: absolute;
  top: 10%;
  left: 12%;
  width: 76%;
  height: 66%;
  overflow: hidden;
  border-radius: 6px;
  z-index: 1;
}

.computer-content {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* 左侧导航与整体布局 */
.monitoring-layout {
  width: 100%;
  max-width: 1200px;
  margin: 2rem auto 0;
  padding: 0 2rem;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 36px;
}

.vertical-tabs {
  width: 260px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.vertical-tab {
  background: #0f1322;
  color: #d1d5e6;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 20px 12px;
  height: 64px;
  text-align: center;
  font-size: 14px;
  line-height: 24px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.35);
  cursor: pointer;
  transition: all 0.25s ease;
  margin-bottom: 26px;
}

.vertical-tab:hover {
  filter: brightness(1.05);
}

.vertical-tab.active {
  background: linear-gradient(135deg, #6a4df7 0%, #e24a4a 100%);
  color: #ffffff;
  box-shadow: 0 10px 28px rgba(98, 74, 226, 0.35);
  border-color: transparent;
}

@media (max-width: 1024px) {
  .monitoring-layout {
    flex-direction: column;
    align-items: center;
  }
  .vertical-tabs {
    width: 100%;
    max-width: 520px;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
  .vertical-tab {
    flex: 1 1 45%;
  }
}

/* 指示点样式 */
.computer-indicators {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.indicator-oval {
  width: 20px;
  height: 8px;
  border-radius: 4px;
  background-color: #ff4444;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ff4444;
  cursor: pointer;
  transition: all 0.3s ease;
}

.laptop-mockup-new {
  position: relative;
  perspective: 1000px;
}

.laptop-screen-new {
  width: 600px;
  height: 380px;
  background: #1a1a1a;
  border-radius: 12px 12px 0 0;
  border: 8px solid #2c3e50;
  border-bottom: none;
  transform: rotateX(8deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  overflow: hidden;
}

.screen-image-new {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.laptop-base-new {
  width: 620px;
  height: 20px;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  border-radius: 0 0 20px 20px;
  position: absolute;
  top: 380px;
  left: -10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.laptop-base-new::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 4px;
  background: #7f8c8d;
  border-radius: 2px;
}

.laptop-dots-new {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 2rem;
}

.dot-new {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: background-color 0.3s;
}

.dot-new.active {
  background: #e74c3c;
}

/* Scenarios Section */
.scenarios-section {
  position: relative;
  background: #ffffff;
  padding: 120px 8% 6rem 8%;
  z-index: 1;
}

.scenarios-title {
  font-size: 2.5rem;
  font-weight: 500;
  text-align: center;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.scenarios-subtitle {
  font-size: 1rem;
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 4rem;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.scenario-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
}

.scenario-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 28px rgba(0, 0, 0, 0.1);
}

/* 中间卡片特殊样式 */
.middle-card {
  position: relative;
}

.middle-card .hover-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
              url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop') center/cover;
  opacity: 0;
  transition: opacity 0.4s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
}

.middle-card:hover .hover-content {
  opacity: 1;
}

.hover-overlay {
  text-align: center;
  color: white;
  padding: 2rem;
  max-width: 300px;
}

.hover-overlay h3 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: inline-block;
}

.hover-overlay p {
  font-size: 15px;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 400;
}

.scenario-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
  display: block;
}

.scenario-content {
  padding: 1rem 1rem 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.scenario-content h3 {
  font-size: 1.05rem;
  font-weight: 600;
  margin: 0.5rem 0 0.4rem;
  color: #333333;
  line-height: 1.35;
}

.scenario-content p {
  font-size: 13px;
  color: #717171;
  line-height: 1.6;
}

.scenario-tags {
  display: flex;
  gap: 0.6rem;
  margin-top: auto;
}

.tag {
  background: #f5f5f5;
  color: #666666;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

/* Footer */
.footer {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 4rem 8% 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 4rem;
}

.footer-logo img {
  height: 30px;
  margin-bottom: 1rem;
}

.footer-desc {
  color: #bdc3c7;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: background-color 0.3s;
}

.social-link:hover {
  background: #e74c3c;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.link-group h4 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 16px;
}

.link-group a {
  display: block;
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  margin-bottom: 0.5rem;
  transition: color 0.3s;
}

.link-group a:hover {
  color: #e74c3c;
}

.footer-contact h4 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 14px;
  color: #bdc3c7;
}

.contact-icon {
  font-size: 16px;
}

.footer-bottom {
  max-width: 1200px;
  margin: 2rem auto 0;
  padding-top: 2rem;
  border-top: 1px solid #34495e;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  color: #bdc3c7;
  font-size: 14px;
}

.footer-bottom-links {
  display: flex;
  gap: 2rem;
}

.footer-bottom-links a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.footer-bottom-links a:hover {
  color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-title {
    font-size: 1.8rem;
  }
  
  .top-left-card {
    left: 8%;
  }
  
  .bottom-left-card {
    left: 5%;
  }
  
  .top-right-card {
    right: 5%;
  }

  .monitoring-display-new {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .laptop-screen-new {
    width: 500px;
    height: 320px;
  }
}

@media (max-width: 1024px) {
  .header-content {
    padding: 0 1rem;
  }

  .nav {
    gap: 1rem;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .scenario-card {
    height: 220px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 1rem;
  }

  .nav {
    margin: 1rem 0;
  }

  .header-right {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .hero-section {
    padding: 0 5%;
    margin-top: 120px;
  }

  .hero-title {
    font-size: 1.2rem;
  }

  .text-features-container {
    padding: 4rem 0;
  }

  .text-section,
  .features-section,
  .scenarios-section {
    padding: 2rem 5%;
  }

  .main-text {
    font-size: 1.3rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .monitoring-title-new {
    font-size: 2.2rem;
  }

  .monitoring-tabs-new {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .laptop-screen-new {
    width: 350px;
    height: 220px;
  }

  .monitoring-stats-new {
    gap: 0.5rem;
  }

  .computer-display-container {
    margin: 2rem auto 0;
    padding: 0 1rem;
    min-height: 300px;
  }

  .computer-image-wrapper {
    max-width: 100%;
  }

  .computer-image {
    max-width: 100%;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .scenario-card {
    height: 200px;
  }

  .scenario-content {
    padding: 1rem;
  }

  .scenario-content h3 {
    font-size: 1.1rem;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  /* Redesigned section mobile */
  .top-section {
    top: 5%;
    width: 95%;
  }
  
  .main-title {
    font-size: 1.4rem;
  }
  
  .subtitle {
    font-size: 0.9rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  
  .card {
    min-width: 160px;
    padding: 16px;
  }
  
  .top-left-card {
    top: 45%;
    left: 5%;
  }
  
  .bottom-left-card {
    bottom: 15%;
    left: 5%;
  }
  
  .top-right-card {
    top: 45%;
    right: 5%;
  }

}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1rem;
  }

  .main-text {
    font-size: 1.1rem;
  }

  .monitoring-title-new {
    font-size: 1.8rem;
  }

  .scenarios-title {
    font-size: 1.5rem;
  }

  .laptop-screen-new {
    width: 280px;
    height: 180px;
  }

  .monitoring-tabs-new {
    flex-direction: column;
    align-items: center;
  }

  /* Redesigned section mobile */
  .monitoring-section-redesigned {
    min-height: 120vh;
    padding: 60px 0 100px 0;
    background-size: 100% 100%;
  }

  .monitoring-content-new {
    min-height: 120vh;
  }

  .main-title {
    font-size: 1.2rem;
  }
  
  .card {
    min-width: 140px;
    padding: 12px;
  }
  
  .card h3 {
    font-size: 14px;
  }
  
  .card-subtitle,
  .card-item {
    font-size: 12px;
  }
}
.left-top-image{
  position: absolute;
  top:41%;
  left:22%;
}
.left-button-image{
  position: absolute;
  top:60%;
  left:15%;
}
.left-xian-image{
  position: absolute;
  top:54%;
  right:17%;
}
</style>
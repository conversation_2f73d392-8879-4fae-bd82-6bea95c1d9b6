<template>
  <div class="building-control">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background"></div>
      <div class="hero-content">
        <div class="container">
          <h1 class="hero-title">智能楼宇控制解决方案</h1>
          <p class="hero-subtitle">
            基于数字孪生技术构建工业及办公园区能源中枢，集成光伏、储能与市电多源供能系统，实现对照明、空调、生产线设备等全链路用能单元的监控与策略化调控，驱动园区零碳运行。
          </p>
          <div class="hero-video-link">
            <div class="play-icon"></div>
            <span>Watch our video</span>
          </div>
          <img
            src="/images/building-hero.png"
            alt="Building Hero Image"
            class="hero-main-image"
          />
        </div>
      </div>
    </section>

    <!-- Engine Section -->
    <section class="engine-section">
      <div class="engine-container">
        <div class="top-section">
          <div class="solution-badge">碳平衡解决方案</div>
          <h1 class="main-title">四维一体管控引擎</h1>
          <p class="subtitle">
            全设备能耗追踪+微网自供优化+碳流热力图预警+AI策略沙盘推演，实现"监控-调控-验证"闭环。
          </p>
          <div class="action-buttons">
            <button class="btn-smart active">
              <span class="btn-icon">●</span>
              全域能耗透视
            </button>
            <button class="btn-smart">
              <span class="btn-icon">●</span>
              碳排决策看板
            </button>
          </div>
        </div>

        <div class="engine-display">
          <img
            src="/images/banner3.png"
            alt="Building Control Engine"
            class="engine-main-image"
          />

          <!-- Overlay Cards -->
          <div class="card top-left-card">
            <div class="card-header">
              <div class="card-icon red-icon"></div>
              <h3>全域能耗透视</h3>
            </div>
            <p class="card-subtitle">
              传感器节点实时采集耗能设备电流/功率数据，定位高耗能设备
            </p>
          </div>

          <div class="card bottom-left-card">
            <div class="card-header">
              <div class="card-icon red-icon"></div>
              <h3>负荷控制系统</h3>
            </div>
            <div class="card-items">
              <div class="card-item">手动精细化控制</div>
              <div class="card-item">自动执行节能策略</div>
            </div>
          </div>

          <div class="card top-right-card">
            <div class="card-header">
              <h3>碳排决策看板</h3>
              <div class="card-icon check-icon"></div>
            </div>
            <div class="card-items">
              <div class="card-item">设备能效数据</div>
              <div class="card-item">高碳排设备排行</div>
              <div class="card-item">AI优化分析</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Text and Features Container -->
    <section class="text-features-container">
      <div class="container">
        <div class="features-content">
          <div class="features-header">
            <span class="badge">智能楼宇控制面临的挑战</span>
            <h2 class="features-title">
              数字孪生深度赋能能源中枢，全链路智能管控驱动节能增效新突破
            </h2>
          </div>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon red">
                <img src="/images/icon01.svg" alt="" />
              </div>
              <h3>多源供能割裂，能源调配效率低</h3>
            </div>
            <div class="feature-card">
              <div class="feature-icon red">
                <img src="/images/icon02.svg" alt="" />
              </div>
              <h3>全链路设备用能分散，状态难掌握</h3>
            </div>
            <div class="feature-card">
              <div class="feature-icon red">
                <img src="/images/icon03.svg" alt="" />
              </div>
              <h3>设备能耗调控盲目，浪费严重</h3>
            </div>
            <div class="feature-card">
              <div class="feature-icon red">
                <img src="/images/icon04.svg" alt="" />
              </div>
              <h3>供能异常难定位，处置耗时长</h3>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Monitoring Section -->
    <section class="monitoring-section">
      <div class="container">
        <div class="monitoring-content">
          <div class="section-header">
            <h2 class="monitoring-title">
              构建起从能源供应、实时监测、智能调<br />控到策略优化的闭环管理体系
            </h2>
            <p class="monitoring-subtitle">
              数字孪生赋能，能源中枢管控全链节能
            </p>
          </div>
          <div class="monitoring-layout">
            <div class="monitoring-tabs">
              <button
                v-for="(tab, index) in monitorTabs"
                :key="index"
                :class="['tab-btn', { active: activeMonitorTab === index }]"
                @click="activeMonitorTab = index"
              >
                {{ tab }}
              </button>
            </div>
            <div class="monitoring-display">
              <img
                src="/images/容器 8.png"
                alt="Building Control Dashboard"
                class="dashboard-image"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Scenarios Section -->
    <section class="scenarios-section">
      <div class="container">
        <h2 class="section-title">智能楼宇控制应用场景</h2>
        <p class="section-subtitle">
          三维透视电网资产，AI秒级定位故障，守护城市能源命脉
        </p>
        <div class="scenarios-grid">
          <div class="scenario-card">
            <img
              src="/images/办公写字楼.png"
              alt="办公写字楼"
              class="scenario-image"
            />
            <div class="scenario-content">
              <h3>办公写字楼</h3>
              <p>AI人流联动：人离灯熄+空调调温，实时能耗值</p>
            </div>
          </div>
          <div class="scenario-card">
            <img
              src="/images/商业综合体.png"
              alt="商业综合体"
              class="scenario-image"
            />
            <div class="scenario-content">
              <h3>商业综合体</h3>
              <p>客流驱动新风：人流热力图联动风量，碳排显著下降</p>
            </div>
          </div>
          <div class="scenario-card">
            <img
              src="/images/疗养园区.png"
              alt="医疗园区"
              class="scenario-image"
            />
            <div class="scenario-content">
              <h3>医疗园区</h3>
              <p>生命线三级保障：光伏+储能+柴油15ms无缝切换</p>
            </div>
          </div>
          <div class="scenario-card">
            <img
              src="/images/工业智造园.png"
              alt="工业制造园"
              class="scenario-image"
            />
            <div class="scenario-content">
              <h3>工业制造园</h3>
              <p>谷电预生产策略：生产策略调控，电力费用综合下降</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const activeMonitorTab = ref(0);
const monitorTabs = [
  "实时零碳值",
  "用能监控",
  "资产管理",
  "智能楼控",
  "能源流向",
];
</script>

<style scoped>
/* General */
.building-control {
  background-color: #ffffff;
  color: #333;
  font-family: "Helvetica Neue", Arial, sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero {
  position: relative;
  display: flex;
  align-items: center;
  text-align: center;
  color: white;
  padding: 120px 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #1e1f25;
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 18px;
  max-width: 700px;
  margin: 0 auto 30px;
  color: #fafafa;
  line-height: 1.6;
}

.hero-video-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 40px;
  cursor: pointer;
}

.play-icon {
  width: 30px;
  height: 30px;
  border: 2px solid white;
  border-radius: 50%;
  position: relative;
}

.play-icon::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-40%, -50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 10px solid white;
}

.hero-video-link span {
  font-size: 16px;
  font-weight: 500;
}

.hero-main-image {
  max-width: 100%;
  margin-top: 30px;
}

/* Engine Section */
.engine-section {
  padding: 80px 0;
  background-color: #1e1f25;
  color: white;
  text-align: center;
}

.engine-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.engine-section .top-section {
  margin-bottom: 50px;
}

.engine-section .solution-badge {
  display: inline-block;
  padding: 6px 15px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 15px;
  font-size: 14px;
  margin-bottom: 20px;
}

.engine-section .main-title {
  font-size: 40px;
  font-weight: 700;
  margin-bottom: 15px;
}

.engine-section .subtitle {
  font-size: 16px;
  color: #b0b3b8;
  max-width: 600px;
  margin: 0 auto 30px;
}

.engine-section .action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.engine-section .btn-smart {
  background-color: #ffffff;
  color: #080712;
  padding: 10px 25px;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.engine-section .btn-smart:hover,
.engine-section .btn-smart.active {
  background-color: #fff;
  color: #1e1f25;
}

.engine-display {
  position: relative;
  margin-top: 40px;
}

.engine-main-image {
  max-width: 800px;
  width: 100%;
}

.engine-display .card {
  position: absolute;
  background-color: #ffffff;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 20px;
  text-align: left;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  width: 280px;
}

.engine-display .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.engine-display .card-header h3 {
  color: #080712;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.engine-display .card-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #e74c3c; /* Default red */
}

.engine-display .check-icon {
  background-color: #2ecc71; /* Green for check */
}

.engine-display .card-subtitle {
  font-size: 14px;
  color: #505360;
  line-height: 1.5;
}

.engine-display .card-items .card-item {
  font-size: 14px;
  color: #505360;
  margin-bottom: 8px;
  padding-left: 15px;
  position: relative;
}

.engine-display .top-left-card {
  top: 20px;
  left: 0;
}

.engine-display .bottom-left-card {
  bottom: 40px;
  left: 50px;
}

.engine-display .top-right-card {
  top: 50px;
  right: 0;
}

/* Text and Features Container */
.text-features-container {
  padding: 80px 0;
  background-image: url("/public/images/有一家设计素材铺淘宝店 <EMAIL>");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.text-features-container .features-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.text-features-container .features-header {
  text-align: center;
  margin-bottom: 2rem;
}

.text-features-container .features-header .badge {
  display: inline-block;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.4);
  border: 1.4px solid #ffffff;
  color: #505360;
  border-radius: 16px;
  font-weight: 600;
  margin-bottom: 20px;
}

.text-features-container .features-header .features-title {
  font-size: 32px;
  font-weight: 500;
  color: #212529;
  line-height: 1.4;
  max-width: 870px;
  margin: 0 auto;
}

.text-features-container .features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  width: 100%;
}

.text-features-container .feature-card {
  background-color: #ffffff;
  padding: 40px 25px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
  gap: 20px;
}

.text-features-container .feature-icon {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.text-features-container .feature-icon img {
  width: 28px;
  height: 28px;
}

.text-features-container .feature-card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #343a40;
  margin: 0;
}

/* Monitoring Section */
.monitoring-section {
  padding: 100px 0;
  background: url('/public/images/BG@1x (1).png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center -120px; /* 向上偏移背景，可按需调整 */
  color: white;
  position: relative;
  overflow: hidden;
}

.monitoring-section::before,
.monitoring-section::after {
  content: "";
  position: absolute;
  left: 50%;
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 0; /* Background waves */
}

.monitoring-section::before {
  width: 180%;
  height: 180%;
  top: -165%;
  /* background-color: rgba(255, 255, 255, 0.05); */
}

.monitoring-section::after {
  width: 175%;
  height: 175%;
  top: -160%;
  /* background-color: #0a102e; */
}

.monitoring-section .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative; /* Establishes stacking context for content */
  z-index: 1; /* Ensures content is above background waves */
}

.monitoring-content {
  text-align: center;
}

.section-header {
  margin-bottom: 50px;
}

.monitoring-title {
  font-size: 36px;
  font-weight: 400;
  margin-bottom: 15px;
}

.monitoring-subtitle {
  font-size: 18px;
  color: #b0b3b8;
}

.monitoring-layout {
  display: flex;
  align-items: stretch;
  text-align: left;
  background: #0f172a;
}

.monitoring-tabs {
  display: flex;
  flex-direction: column;
  gap: 15px; /* Add gap for spacing */
  flex-shrink: 0;
}

.tab-btn {
  background: #080712;
  border: 1px solid #2f3241;
  color: white;
  padding: 15px 25px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  width: 200px;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-btn.active {
  background: linear-gradient(90deg, #623cea, #e93a78);
  border-color: transparent;
  box-shadow: none;
}

.monitoring-display {
  flex-grow: 1;
  margin-left: 20px;
}

.dashboard-image {
  width: 100%;
  border-radius: 8px;
}

/* Scenarios Section */
.scenarios-section {
  padding: 100px 0;
  background-color: #ffffff;
  text-align: center;
}

.scenarios-section .section-title {
  font-size: 36px;
  color: #2c3e50;
  margin-bottom: 15px;
}

.scenarios-section .section-subtitle {
  font-size: 18px;
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto 50px;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  text-align: left;
}

.scenario-card {
  border-radius: 8px;
  overflow: hidden;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.scenario-card:hover {
  transform: translateY(-5px);
}

.scenario-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.scenario-content h3 {
  font-size: 20px;
  margin-top: 0;
  margin-top: 0;
  margin-top: 10px;
  margin-bottom: 10px;
  color: #2c3e50;
}

.scenario-content p {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 992px) {
  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .text-features-container {
    flex-direction: column;
    gap: 40px;
  }
  .monitoring-layout {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 32px;
  }
  .engine-display .card {
    position: static;
    width: auto;
    margin-bottom: 20px;
  }
  .features-grid {
    grid-template-columns: 1fr;
  }
  .scenarios-grid {
    grid-template-columns: 1fr;
  }
}
</style>

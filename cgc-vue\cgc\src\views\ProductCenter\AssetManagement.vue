<template>
  <div class="asset-management">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            设备全生命周期健康预诊，<br />让电力资产零隐患运行
          </h1>
          <img src="/images/products/red-line.png" alt="" class="red-line" />
          <p class="hero-subtitle">
            Using the cloud for storage gives you access files from anywhere
            with an internet connection very easily.
          </p>
        </div>
        <div class="hero-dashboard">
          <img
            src="/images/products/断路器.png"
            alt="管理平台预览"
            class="dashboard-image"
          />
        </div>
      </div>
    </section>

    <!-- Standards Section -->
    <section class="standards">
      <div class="container">
        <h2 class="standards-title">
          We always follow the standard <br />
          work process
        </h2>
        <p class="standards-subtitle">
          严格遵循标准化工作流程，确保产品质量与服务水准
        </p>

        <div class="standards-grid">
          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="white"
                    stroke-width="2"
                  />
                  <polyline
                    points="12,6 12,12 16,14"
                    stroke="white"
                    stroke-width="2"
                  />
                </svg>
              </div>
            </div>
            <h3>技术实现</h3>
            <p>失效模型分析中低压设备老化趋势，LSTM算法预测故障概率达92%精度</p>
          </div>

          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 11H15M9 15H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L18.7071 8.70711C18.8946 8.89464 19 9.149 19 9.41421V19C19 20.1046 18.1046 21 17 21Z"
                    stroke="white"
                    stroke-width="2"
                  />
                </svg>
              </div>
            </div>
            <h3>方案设计</h3>
            <p>
              基于需求分析结果，设计最优的技术方案，确保系统稳定性和可扩展性
            </p>
          </div>

          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 2L2 7L12 12L22 7L12 2Z"
                    stroke="white"
                    stroke-width="2"
                  />
                  <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2" />
                  <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2" />
                </svg>
              </div>
            </div>
            <h3>产品实施</h3>
            <p>严格按照设计方案进行产品开发和部署，保证项目按时交付</p>
          </div>

          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <polyline
                    points="20,6 9,17 4,12"
                    stroke="white"
                    stroke-width="2"
                  />
                </svg>
              </div>
            </div>
            <h3>测试验收</h3>
            <p>全面测试产品功能，确保达到预期效果，提供完善的售后服务</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Map Section -->
    <section class="map-section">
      <div class="container">
        <h2 class="map-title">三层架构层创新，全面覆盖资产管理全场景</h2>
        <p class="map-subtitle">
          以数据驱动-智能分析-精准执行三层架构，构建智能、高效、全面的资产管理生态。
        </p>
        <div class="map-container">
          <img
            src="/images/products/map.png"
            alt="资产管理架构图"
            class="map-image"
          />
        </div>
      </div>
    </section>

    <!-- Product Introduction Section -->
    <section class="product-intro">
      <div class="container">
        <h2 class="intro-title">产品介绍</h2>

        <!-- First Row: Left Image, Right Content -->
        <div class="intro-row">
          <div class="intro-image">
            <img
              src="/images/products/健康概览.png"
              alt="资产管理系统"
              class="product-image"
            />
          </div>
          <div class="intro-content">
            <h3>资产管理系统（APM）</h3>
            <p>
              从资产的规划设计到现场调试运营方案，一直到资产的健康管理，能够收集和分析
              所有资产的生命周期数据，实现资产全生命周期的健康管理，让每台设备的健康
              数据可视，在全面掌握设备运行状况的基础上，通过智能分析全生命周期资产内在的规律，
              在设备问题发生前进行预警，从而实现设备的健康管理。
            </p>
          </div>
        </div>

        <!-- Second Row: Left Content, Right Image -->
        <div class="intro-row reverse">
          <div class="intro-image">
            <img
              src="/images/products/健康概览—爆炸视角.png"
              alt="三维模块化组件"
              class="product-image"
            />
          </div>
          <div class="intro-content">
            <h3>三维模块化多视角多功能组件</h3>
            <p>
              多功能整合，通过技术高度集成的4G+5G智能终端，提供可视之外的
              空间感受，管理技术人员可以更直观地掌握设备运行状态，结合智能分析的
              交互，提高了设备运行可视化程度，有效提升了设备管理效率，
              交互，提高了可视效率。
            </p>
            <button class="learn-more-btn">三维可视化孪生</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Customers Section -->
    <section class="customers">
      <div class="container">
        <div class="customers-header">
          <h2 class="customers-title">
            <span class="title-line">Customers we</span>
            <span class="title-line">serve</span>
          </h2>
          <div class="carousel-nav">
            <button @click="prev" class="nav-btn prev-btn">
              <svg width="24" height="24" viewBox="0 0 24 24">
                <path
                  d="M15 18l-6-6 6-6"
                  stroke="currentColor"
                  stroke-width="2"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
            <button @click="next" class="nav-btn next-btn">
              <svg width="24" height="24" viewBox="0 0 24 24">
                <path
                  d="M9 18l6-6-6-6"
                  stroke="currentColor"
                  stroke-width="2"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
        <div class="customers-container">
          <el-carousel
            ref="carousel"
            arrow="never"
            :interval="4000"
            indicator-position="none"
            @change="handleCarouselChange"
          >
            <el-carousel-item v-for="item in 1" :key="item">
              <div class="customers-grid">
                <div class="customer-card">
                  <div class="customer-header">
                    <div class="customer-icon">
                      <div class="icon-building"></div>
                    </div>
                    <div class="customer-info">
                      <h4>****集团</h4>
                      <p class="customer-subtitle">工业制造领域</p>
                    </div>
                  </div>
                  <p class="customer-description">
                    企业级资产信息化OPP、BOPETG内外多功能薄膜、聚苯乙烯、PET、工业级多个领域，随着数字化信息化深度融合的不断深化，其基础数字化管理的深度融合进程已成为大趋势。
                  </p>
                </div>

                <div class="customer-card">
                  <div class="customer-header">
                    <div class="customer-icon">
                      <div class="icon-building"></div>
                    </div>
                    <div class="customer-info">
                      <h4>****集团</h4>
                      <p class="customer-subtitle">工业制造领域</p>
                    </div>
                  </div>
                  <p class="customer-description">
                    大型电力系统资产管理智能安全、经济、清洁、可持续的能源供应，工厂智能化的数字化转型，准确性、高效性、高精度实现数字化转型，智能化生产、智能化运营管理等智能化管理体系。
                  </p>
                </div>

                <div class="customer-card">
                  <div class="customer-header">
                    <div class="customer-icon">
                      <div class="icon-building"></div>
                    </div>
                    <div class="customer-info">
                      <h4>****集团</h4>
                      <p class="customer-subtitle">工业制造领域</p>
                    </div>
                  </div>
                  <p class="customer-description">
                    企业级资产信息化OPP、BOPETG内外多功能薄膜、聚苯乙烯、PET、工业级多个领域，随着数字化信息化深度融合的不断深化，其基础数字化管理的深度融合进程已成为大趋势。
                  </p>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { CarouselInstance } from "element-plus";

const carousel = ref<CarouselInstance>();

const prev = () => {
  carousel.value?.prev();
};

const next = () => {
  carousel.value?.next();
};

const handleCarouselChange = (index: number) => {
  // This function is ready for logic to change button styles if needed.
};

// 资产管理页面逻辑
</script>

<style scoped>
.customers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.carousel-nav {
  display: flex;
  gap: 10px;
}

.nav-btn {
  background-color: #333;
  border: 1px solid #555;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.nav-btn.prev-btn {
  background-color: white;
  color: black;
}

.nav-btn.next-btn {
  background-color: #3a3a3a;
  color: #888;
}

.nav-btn:hover {
  background-color: #555;
}

.nav-btn.prev-btn:hover {
  background-color: #eee;
}
</style>

<style scoped>
.asset-management {
  padding-top: 80px; /* 为主导航+二级导航预留空间 */
  min-height: 100vh;
  background: #1e1f25;
  color: #ffffff;
}

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1280px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
  gap: 80px;
}

.hero-text {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 20px;
  color: #ffffff;
}
.red-line {
  width: 70%;
}
.hero-subtitle {
  font-size: 18px;
  line-height: 1.6;
  color: #b0b3b8;
  margin-bottom: 40px;
}

.hero-dashboard {
  flex: 1;
  text-align: center;
}

.dashboard-image {
  max-width: 100%;
  height: auto;
}

/* Standards Section */
.standards {
  background: #2a2b31;
  padding: 120px 0;
}

.container {
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 20px;
}

.standards-title {
  font-size: 48px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 20px;
  color: #ffffff;
}

.standards-subtitle {
  font-size: 18px;
  text-align: center;
  color: #b0b3b8;
  margin-bottom: 80px;
}

.standards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 60px;
  max-width: 1200px;
  margin: 0 auto;
}

.standard-item {
  border-right: 1px solid #666;
  padding-right: 30px;
}

.standard-item:last-child {
  border-right: none;
}

.standard-icon {
  margin-bottom: 30px;
}

.icon-circle {
  width: 80px;
  height: 80px;
  background: #ca2a22;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
}

.standard-item h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffffff;
}

.standard-item p {
  font-size: 16px;
  line-height: 1.6;
  color: #b0b3b8;
}

/* Customers Section */
.customers {
  padding: 120px 0;
  background: #1e1f25;
}

.customers-header {
  margin-bottom: 80px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.customers-title {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  line-height: 1.2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.title-line {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
}

.title-line::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: calc(100% + 200px);
  height: 4px;
  background: #ca2a22;
}

.customers-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
}

.customer-line-decoration {
  position: absolute;
  top: -200px;
  left: -120px;
  width: 700px;
  height: auto;
  z-index: 0;
  opacity: 0.6;
}

.customer-line-decoration img {
  width: 100%;
  height: auto;
  display: block;
}

.customers-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  position: relative;
  z-index: 1;
}

.customer-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.customer-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.customer-icon {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.icon-building {
  width: 100%;
  height: 100%;
  background: #ca2a22;
  border-radius: 8px;
  position: relative;
}

.icon-building::before {
  content: "🏢";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: white;
}

.customer-info {
  flex: 1;
}

.customer-info h4 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.customer-subtitle {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.customer-description {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #666;
  margin: 0;
}

/* Map Section */
.map-section {
  padding: 80px 0;
  background: #1e1f25;
}

.map-title {
  font-size: 2rem;
  font-weight: 500;
  text-align: center;
  margin-bottom: 20px;
  color: white;
}

.map-subtitle {
  text-align: center;
  color: #ca2a22;
  font-size: 1.1rem;
  margin-bottom: 60px;
  line-height: 1.6;
}

.map-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.map-image {
  width: 100%;
  height: auto;
  border-radius: 10px;
}

/* Product Introduction Section */
.product-intro {
  padding: 80px 0;
  background: #1e1f25;
}

.intro-title {
  font-size: 2.5rem;
  font-weight: 500;
  text-align: center;
  margin-bottom: 80px;
  color: white;
}

.intro-row {
  display: flex;
  align-items: center;
  gap: 80px;
  margin-bottom: 100px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.intro-row.reverse {
  flex-direction: row-reverse;
}

.intro-image {
  flex: 1;
  max-width: 600px;
}

.product-image {
  width: 100%;
  height: auto;
}

.intro-content {
  flex: 1;
  max-width: 600px;
}

.intro-content h3 {
  font-size: 2rem;
  font-weight: 500;
  color: white;
  margin-bottom: 30px;
  line-height: 1.3;
}

.intro-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #b0b3b8;
  margin-bottom: 30px;
}

.learn-more-btn {
  background: #000000;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.learn-more-btn:hover {
  background: #a02219;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-content {
    max-width: 1200px;
    gap: 60px;
  }

  .standards-grid {
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .asset-management {
    padding-top: 140px;
  }

  .hero {
    height: auto;
    padding: 60px 0;
  }

  .hero-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 36px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .standards-title {
    font-size: 36px;
  }

  .standards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }

  .customers-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .customers-title {
    font-size: 2rem;
  }

  .customers-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 28px;
  }

  .standards-title {
    font-size: 28px;
  }

  .standards-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .intro-row {
    flex-direction: column;
    gap: 40px;
    margin-bottom: 60px;
  }

  .intro-row.reverse {
    flex-direction: column;
  }

  .intro-content h3 {
    font-size: 1.5rem;
  }

  .map-title {
    font-size: 2rem;
  }

  .intro-title {
    font-size: 2rem;
  }

  .customers-title {
    font-size: 1.5rem;
  }
}
</style>

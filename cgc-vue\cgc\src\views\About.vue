<template>
  <div class="about">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-graphics">
          <!-- Background Images -->
          <img
            src="/images/about/bg-green.png"
            alt="Green cube"
            class="bg-green"
          />
          <img
            src="/images/about/dotted.png"
            alt="Dotted frame"
            class="dotted-frame"
          />
          <img
            src="/images/about/pode.png"
            alt="Decoration"
            class="po-decoration"
          />
        </div>
      </div>
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            在每度电都值得被精准管理的时<br />
            代，我们正在构建能源流动的智能<br />
            镜像世界
          </h1>
          <p class="hero-subtitle">
            CGC
            DIGITAL致力于通过先进的数字化技术，为全球客户提供智能化的能源管理解决方案，<br />
            构建更加高效、可持续的能源生态系统。
          </p>
          <p class="hero-description">
            我们相信，在数字化转型的浪潮中，每一度电都应该被精准管理，<br />
            每一个能源流动都应该被智能优化。
          </p>
        </div>
      </div>
    </section>

    <!-- Statistics Section -->
    <section class="statistics">
      <div class="content-container">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-number">15</div>
              <div class="stat-text">
                <div class="stat-sub">Years of <br />experience</div>
              </div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-number">36k</div>
              <div class="stat-text">
                <div class="stat-sub">
                  Satisfied clients <br />around the world
                </div>
              </div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-number">6428</div>
              <div class="stat-text">
                <div class="stat-sub">
                  Project completed on <br />25 countries
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Why Choose CGC Section -->
    <section class="why-choose">
      <div class="content-container">
        <div class="why-choose-content">
          <div class="why-choose-left">
            <h2 class="section-title">为什么客户选择CGC DIGITAL</h2>
            <p class="section-description">
              全周期零事故守护，碳能双降本硬核兑现，兆瓦至千瓦全域智控，闭环实证高回报
            </p>
            <div class="company-info">
              <div class="logo-section"></div>
            </div>
          </div>
          <div class="why-choose-right">
            <div class="choose-circles">
              <div class="circle-grid">
                <div class="circle-item circle-1">
                  <div class="circle-content">
                    <div class="circle-text">零危运维</div>
                  </div>
                </div>
                <div class="circle-item circle-2">
                  <div class="circle-content">
                    <div class="circle-text">全域智控</div>
                  </div>
                </div>
                <div class="circle-item circle-3">
                  <div class="circle-content">
                    <div class="circle-text">碳能双降</div>
                  </div>
                </div>
                <div class="circle-item circle-4">
                  <div class="circle-content">
                    <div class="circle-text">实证高ROI</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="video-placeholder">
          <img
            src="/images/about/videoimg.png"
            alt="Company Video"
            class="video-thumbnail"
          />
        </div>
      </div>
    </section>

    <!-- Work Process Section -->
    <section class="work-process">
      <div class="content-container">
        <h2 class="process-title">
          We always follow the standard <br />
          work process
        </h2>
        <p class="process-description">
          从蓝图到碳足迹的AI闭环：让每度电的生命周期价值提升
        </p>
        <div class="process-flow">
          <svg class="process-dashed-ellipse">
            <ellipse cx="50%" cy="50%" rx="52%" ry="48%" />
          </svg>
          <!-- Top Row -->
          <div class="process-row top-row">
            <div class="process-item">
              <div class="process-icon">
                <img src="/images/about/01.png" alt="" />
              </div>
              <h3>DEFINE</h3>
              <div class="process-details">
                <p>
                  Requirements Development <br />
                  Portable Metering Studies <br />
                  Power Quality Audits
                </p>
              </div>
            </div>
            <div class="process-arrow">
              <img
                src="/images/about/路径.png"
                alt="arrow"
                class="arrow-image"
              />
            </div>
            <div class="process-item">
              <div class="process-icon">
                <img src="/images/about/02.png" alt="" />
              </div>
              <h3>DESIGN</h3>
              <div class="process-details">
                <p>
                  Network Infrastructure <br />
                  Control System <br />
                  System & DER Integration
                </p>
              </div>
            </div>
            <div class="process-arrow">
              <img
                src="/images/about/路径.png"
                alt="arrow"
                class="arrow-image"
              />
            </div>
            <div class="process-item">
              <div class="process-icon">
                <img src="/images/about/03.png" alt="" />
              </div>
              <h3>DEPLOY</h3>
              <div class="process-details">
                <p>
                  IP Networking &Equipment <br />
                  with SD-WAN <br />
                  SCADAEPMS System
                </p>
              </div>
            </div>
          </div>

          <!-- Connecting Arrow -->
          <div class="process-connector">
            <div class="connector-arrow">
              <img
                src="/images/about/路径.png"
                alt="down arrow"
                class="arrow-image down-arrow"
              />
            </div>
          </div>

          <!-- Bottom Row -->
          <div class="process-row bottom-row">
            <div class="process-item">
              <div class="process-icon">
                <img src="/images/about/04.png" alt="" />
              </div>
              <h3>COMMISSION</h3>
              <div class="process-details">
                <p>
                  Cloud Infrastructure <br />
                  Data lnterop & Integrations <br />
                  Startup, Train & Handoff
                </p>
              </div>
            </div>
            <div class="process-arrow">
              <img
                src="/images/about/路径.png"
                alt="arrow"
                class="arrow-image left-arrow"
              />
            </div>
            <div class="process-item">
              <div class="process-icon">
                <img src="/images/about/05.png" alt="" />
              </div>
              <h3>SUPPORT</h3>
              <div class="process-details">
                <p>
                  Fleet Management <br />
                  Cybersecurity Assurance <br />
                  Maintenance & Ops Support
                </p>
              </div>
            </div>
            <div class="process-arrow">
              <img
                src="/images/about/路径.png"
                alt="arrow"
                class="arrow-image left-arrow"
              />
            </div>
            <div class="process-item">
              <div class="process-icon">
                <img src="/images/about/06.png" alt="" />
              </div>
              <h3>OPTIMIZE</h3>
              <div class="process-details">
                <p>
                  Data QualityAdvisory <br />
                  Services <br />
                  Power Quality Advisory <br />
                  Services
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="content-container">
        <div class="cta-content">
          <h2 class="cta-title">
            Eliminate energy waste with AI-optimized power systems
          </h2>
          <button class="cta-button">联系我们 ></button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// About page logic
</script>

<style scoped>
.about {
  padding-top: 80px;
  min-height: 100vh;
  background: #1e1f25;
  color: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero {
  position: relative;
  padding: 120px 0 80px;
  background: #1e1f25;
}

.hero-background {
  position: absolute;
  top: 0;
  right: 0;
  width: 60%;
  height: 100%;
  z-index: 1;
}

.hero-graphics {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Background Images */
.bg-green {
  position: absolute;
  top: 0%;
  right: 70%;
  width: 800px;
  height: auto;
  z-index: 3;
}

.dotted-frame {
  position: absolute;
  top: -5%;
  right: 20%;
  width: 320px;
  height: auto;
  z-index: 1;
  opacity: 0.4;
}

.po-decoration {
  position: absolute;
  top: 25%;
  right: 28%;
  width: 400px;
  height: auto;
  z-index: 4;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-text {
  max-width: 65%;
}

.hero-title {
  font-size: 3.2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 30px;
  color: white;
}

.hero-subtitle {
  font-size: 1.1rem;
  color: #b0b0b0;
  line-height: 1.6;
  margin-bottom: 20px;
}

.hero-description {
  font-size: 1rem;
  color: #888;
  line-height: 1.6;
}

/* Statistics Section */
.statistics {
  margin-top: 80px;
  padding: 80px 20px;
  background: #1e1f25;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.stat-item {
  padding: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: center;
}

.stat-number {
  font-size: 4rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stat-text {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.stat-sub {
  font-size: 1rem;
  color: #888;
  line-height: 1.2;
}

/* Why Choose Section */
.why-choose {
  padding: 100px 0;
  background: #1e1f25;
}

.why-choose-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 30px;
  color: white;
  line-height: 1.3;
}

.section-description {
  font-size: 1.1rem;
  color: #b0b0b0;
  line-height: 1.6;
  margin-bottom: 40px;
}

.company-logo {
  width: 120px;
  height: auto;
}

.choose-circles {
  display: flex;
  justify-content: center;
  align-items: center;
}

.circle-grid {
  position: relative;
  width: 400px;
  height: 300px;
  margin: 0 auto;
}

.circle-item {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  transition: transform 0.3s ease;
  background: rgba(47, 50, 65, 0.5);
}

.circle-item:hover {
  transform: scale(1.05);
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 50px;
  left: 50px;
  border: 1px solid #4c6ef5;
}

.circle-2 {
  width: 160px;
  height: 160px;
  top: 80px;
  right: -80px;
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 0px;
  right: 80px;
}

.circle-4 {
  width: 100px;
  height: 100px;
  bottom: 0px;
  right: 70px;
}
.circle-1 .circle-text {
  font-size: 1.5rem;
  color: #ca2a22;
}
.circle-2 .circle-text {
  font-size: 1.5rem;
}
.circle-content {
  text-align: center;
}

.circle-text {
  font-size: 1rem;
  color: #a2a2a6;
}
/* Video Section */
.video-placeholder {
  width: 100%;
  height: auto;
  margin-top: 50px;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Work Process Section */
.work-process {
  padding: 100px 0;
  background: #1e1f25;
}

.process-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 20px;
  color: white;
}

.process-description {
  text-align: center;
  color: #b0b0b0;
  font-size: 1.1rem;
  margin-bottom: 80px;
  line-height: 1.6;
}

.process-flow {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  padding: 40px 0;
}

.process-dashed-ellipse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 90%;
  transform: translate(-50%, -50%) rotate(-8deg);
  z-index: 0;
  overflow: visible;
}

.process-dashed-ellipse ellipse {
  fill: none;
  stroke: #505360;
  stroke-width: 1;
  stroke-dasharray: 4 4;
}

.process-row,
.process-connector {
  position: relative;
  z-index: 1;
}

.process-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  margin-bottom: 40px;
}

.process-row.bottom-row {
  flex-direction: row;
}

.process-connector {
  display: flex;
  justify-content: flex-end;
}

.process-item {
  text-align: left;
  padding: 0;
  min-width: 250px;
}

.process-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 50%;
}

.process-item h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.process-details p {
  color: #ffffff;
  line-height: 1.4;
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.process-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-image {
  width: 100px;
  height: auto;
}

.arrow-image.left-arrow {
  transform: rotate(180deg);
}

.arrow-image.down-arrow {
  transform: rotate(90deg);
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #4c6ef5 0%, #339af0 100%);
}

.cta-content {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 40px;
  line-height: 1.3;
}

.cta-button {
  background: transparent;
  border: 1px solid white;
  color: white;
  padding: 15px 60px;
  font-size: 1.1rem;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cta-button:hover {
  background: white;
  color: #4c6ef5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-text {
    max-width: 70%;
  }

  .why-choose-content {
    gap: 60px;
  }

  .process-row {
    flex-direction: column;
    gap: 30px;
  }

  .process-connector {
    margin-right: 0;
    justify-content: center;
  }

  .arrow-image {
    display: none;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-text {
    max-width: 100%;
  }

  .bg-green,
  .dotted-frame,
  .po-decoration {
    display: none;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .stat-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .stat-text {
    text-align: center;
  }

  .why-choose-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .circle-grid {
    width: 300px;
    height: 250px;
  }

  .circle-1 {
    width: 140px;
    height: 140px;
    top: 10px;
    left: 30px;
  }

  .circle-2 {
    width: 110px;
    height: 110px;
    top: 20px;
    right: 20px;
  }

  .circle-3 {
    width: 80px;
    height: 80px;
    bottom: 60px;
    right: 60px;
  }

  .circle-4 {
    width: 100px;
    height: 100px;
    bottom: 10px;
    left: 10px;
  }

  .circle-text {
    font-size: 1rem;
  }

  .process-row {
    flex-direction: column;
    gap: 20px;
  }

  .process-item {
    min-width: auto;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }

  .process-connector {
    display: none;
  }

  .arrow-image {
    display: none;
  }

  .section-title {
    font-size: 2rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .container {
    padding: 0 15px;
  }
}
</style>
